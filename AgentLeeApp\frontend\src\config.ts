// Configuration file for environment variables

// Extend window interface for custom properties
declare global {
  interface Window {
    GITHUB_PAGES_DEPLOYMENT?: boolean;
    AGENT_LEE_BACKEND_URL?: string;
    AgentLeeCore?: {
      userToken?: string;
    };
  }
}

// Auto-detect environment
const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1');
const isGitHubPages = window.location.hostname.includes('github.io') || window.GITHUB_PAGES_DEPLOYMENT;
const isProduction = !isLocalhost;

// Determine backend URL based on environment
let backendUrl;
if (isGitHubPages) {
  backendUrl = window.AGENT_LEE_BACKEND_URL || 'https://agentlee.fly.dev';
} else if (isProduction) {
  backendUrl = window.AGENT_LEE_BACKEND_URL || 'https://agentlee.fly.dev';
} else {
  backendUrl = 'http://localhost:8000';
}

// Load environment variables with fallbacks
export default {
  // Backend configuration
  backend: {
    url: backendUrl,
    wsUrl: backendUrl.replace('https://', 'wss://').replace('http://', 'ws://')
  },
  
  // LiveKit configuration - auto-configured by backend
  livekit: {
    serverUrl: '', // Will be provided by backend
    roomName: 'agent-lee-room',
    autoConnect: true
  },
  
  // Google API configuration
  google: {
    apiKey: process.env.GOOGLE_API_KEY || 'AIzaSyBpJiw1ym8tTPGI4Mi6a00qSPiudtgoDXo'
  },
  
  // Gemini API configuration (alias for Google API key)
  gemini: {
    apiKey: process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || 'AIzaSyBpJiw1ym8tTPGI4Mi6a00qSPiudtgoDXo'
  },
  
  // Search API configuration
  search: {
    duckduckgoApiKey: process.env.DUCKDUCKGO_SEARCH_API_KEY || '23de90506707108ad5600e4949611d2a1e568cb8be470ba600517eb150e0ba67',
    exaApiKey: process.env.EXA_API_KEY || 'b0b2bcd9-59fd-41cc-aad4-72cc353bd215',
    exaTeamId: process.env.EXA_TEAM_ID || 'Team ID cmdgbry2j0022jhz5dx47qywj'
  },
  
  // Voice configuration
  voice: {
    recognition: {
      language: 'en-US',
      continuous: true,
      interimResults: false
    },
    synthesis: {
      rate: 1,
      pitch: 1,
      volume: 1,
      preferredVoices: ['Male', 'David', 'Mark']
    }
  }
};