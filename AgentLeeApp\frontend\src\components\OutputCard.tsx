import React from 'react';
import { ChatMessage } from '../types';

interface OutputCardProps {
  message: ChatMessage;
}

export const OutputCard: React.FC<OutputCardProps> = ({ message }) => {
  const isUser = message.sender === 'user';
  const content = message.text || message.content || '';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
        isUser 
          ? 'bg-blue-600 text-white rounded-br-sm' 
          : 'bg-gray-700 text-white rounded-bl-sm'
      }`}>
        {!isUser && (
          <div className="flex items-center mb-2">
            <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-sm mr-2">
              🤖
            </div>
            <span className="text-xs text-gray-300">Agent <PERSON></span>
          </div>
        )}
        
        <p className="text-sm leading-relaxed whitespace-pre-wrap">{content}</p>
        
        <div className={`text-xs mt-2 ${isUser ? 'text-blue-100' : 'text-gray-400'}`}>
          {new Date(message.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </div>
    </div>
  );
};
