@echo off
REM Setup script for Agent <PERSON>er environment

REM Check if .env file exists
if not exist .env (
    echo Creating .env file from template...
    copy .env.sample .env
    echo Please edit .env file with your API keys and settings
)

REM Create data directory for persistent storage
if not exist data mkdir data

REM Build and start the containers
echo Building and starting Agent Lee containers...
docker-compose build
docker-compose up -d

echo Agent <PERSON> is now running!
echo - Backend API: http://localhost:5000
echo - Frontend: http://localhost:80
echo - XTTS Service: http://localhost:8080
echo - MCP Hub: http://localhost:3000

echo.
echo To view logs: docker-compose logs -f
echo To stop: docker-compose down

pause