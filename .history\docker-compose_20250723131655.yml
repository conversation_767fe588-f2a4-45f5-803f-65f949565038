version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: nginx_auth
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/htpasswd:/etc/nginx/.htpasswd:ro
    restart: unless-stopped

  tts:
    build: ./xtts
    container_name: tts_server
    ports:
      - "5002:5002"
    environment:
      - CORS_ORIGINS=*
    restart: unless-stopped

  webhook:
    build: ./xtts
    container_name: webhook_server
    command: gunicorn -w 2 -b 0.0.0.0:8080 webhook_server:app
    ports:
      - "8080:8080"
    environment:
      - XTTS_API=http://tts:5002/api/tts
    depends_on:
      - tts
    restart: unless-stopped
