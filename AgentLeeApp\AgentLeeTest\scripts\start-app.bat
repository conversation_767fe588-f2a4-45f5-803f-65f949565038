@echo off
echo ========================================
echo Agent Lee Application Startup
echo ========================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not in PATH
    echo Please install Docker Desktop and try again
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: docker-compose is not available
    echo Please install Docker Compose and try again
    pause
    exit /b 1
)

echo.
echo 🚀 Starting Agent Lee Application...
echo.

REM Create .env file if it doesn't exist
if not exist .env (
    echo Creating .env file from template...
    copy .env.sample .env
    echo Please edit .env file with your API keys and settings
)

REM Create data directory
if not exist data mkdir data

REM Build and start containers
echo Building and starting containers...
docker-compose build
if %errorlevel% neq 0 (
    echo ERROR: Failed to build containers
    pause
    exit /b 1
)

echo Starting services...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ERROR: Failed to start containers
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Agent Lee is now running!
echo ========================================
echo.
echo 📱 Frontend: http://localhost:80
echo 🔧 Backend API: http://localhost:5000
echo 🎤 XTTS Service: http://localhost:8080
echo 🔗 MCP Hub: http://localhost:3000
echo.
echo 📋 Useful commands:
echo   View logs: docker-compose logs -f
echo   Stop app: docker-compose down
echo   Restart: docker-compose restart
echo   Test app: python test-app.py
echo.
echo Press any key to open the application in your browser...
pause >nul

REM Open browser
start http://localhost:80

echo.
echo 🎉 Agent Lee is ready to use!
echo. 