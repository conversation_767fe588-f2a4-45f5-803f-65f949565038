import json
import os
import sqlite3
from datetime import datetime

# File-based memory storage with configurable paths
DATA_DIR = os.environ.get("MEMORY_DATA_DIR", "/app/data")
MEMORY_FILE = os.path.join(DATA_DIR, "memory_store.json")
SPEAKER_HISTORY_FILE = os.path.join(DATA_DIR, "speaker_sessions.json")

# Ensure data directory exists
os.makedirs(DATA_DIR, exist_ok=True)

def load_memory():
    """Load memory from JSON file"""
    if not os.path.exists(MEMORY_FILE):
        return {}
    try:
        with open(MEMORY_FILE, "r") as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}

def save_to_memory(speaker, content):
    """Save content to speaker's memory with timestamp"""
    db = load_memory()
    timestamp = datetime.now().isoformat()

    if speaker not in db:
        db[speaker] = []

    db[speaker].append({
        "content": content,
        "timestamp": timestamp
    })

    with open(MEMORY_FILE, "w") as f:
        json.dump(db, f, indent=2)

def search_memory(speaker=None, keyword=None):
    """Search memory with enhanced filtering"""
    db = load_memory()

    if not speaker:
        # Search across all speakers
        results = []
        for spk, entries in db.items():
            for entry in entries:
                if not keyword or keyword.lower() in entry.get("content", "").lower():
                    results.append({
                        "speaker": spk,
                        "content": entry.get("content", ""),
                        "timestamp": entry.get("timestamp", "")
                    })
        return results

    # Search for specific speaker
    speaker_entries = db.get(speaker, [])
    if not keyword:
        return [{"speaker": speaker, **entry} for entry in speaker_entries]

    return [
        {"speaker": speaker, **entry}
        for entry in speaker_entries
        if keyword.lower() in entry.get("content", "").lower()
    ]

def load_sessions():
    """Load speaker session history"""
    if not os.path.exists(SPEAKER_HISTORY_FILE):
        return {}
    try:
        with open(SPEAKER_HISTORY_FILE, "r") as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}

def save_session(speaker, transcript):
    """Save session transcript for speaker"""
    history = load_sessions()
    timestamp = datetime.now().isoformat()

    if speaker not in history:
        history[speaker] = []

    history[speaker].append({
        "transcript": transcript,
        "timestamp": timestamp
    })

    with open(SPEAKER_HISTORY_FILE, "w") as f:
        json.dump(history, f, indent=2)

# Legacy SQLite compatibility functions
def store_memory(speaker, text, result):
    """Legacy function for backward compatibility"""
    save_to_memory(speaker, f"Input: {text} | Result: {result}")

def get_speaker_stats(speaker):
    """Get statistics for a specific speaker"""
    db = load_memory()
    sessions = load_sessions()

    memory_count = len(db.get(speaker, []))
    session_count = len(sessions.get(speaker, []))

    return {
        "speaker": speaker,
        "memory_entries": memory_count,
        "session_count": session_count,
        "last_activity": db.get(speaker, [{}])[-1].get("timestamp", "Never") if memory_count > 0 else "Never"
    }
