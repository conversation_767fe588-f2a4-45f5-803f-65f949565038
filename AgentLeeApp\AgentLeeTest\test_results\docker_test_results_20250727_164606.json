{"timestamp": "2025-07-27T16:46:06.451900", "results": [{"name": "Docker Installation", "passed": false, "details": "[WinError 2] The system cannot find the file specified", "timestamp": "2025-07-27T16:45:09.614884"}, {"name": "<PERSON><PERSON>", "passed": false, "details": "[WinError 2] The system cannot find the file specified", "timestamp": "2025-07-27T16:45:09.622107"}, {"name": "Container Status", "passed": false, "details": "[WinError 2] The system cannot find the file specified", "timestamp": "2025-07-27T16:45:09.629002"}, {"name": "nginx (Frontend Web Server)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A114790>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:13.690063"}, {"name": "backend (Backend API)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A0BB490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:17.753607"}, {"name": "m<PERSON><PERSON><PERSON> (MCP Hub)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=3000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A115120>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:21.803174"}, {"name": "aura (Emotion AI)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A1159F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:25.855241"}, {"name": "<PERSON><PERSON> (<PERSON>)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=8020): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A1162C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:29.914610"}, {"name": "phone (Phone Control)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=8010): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A116B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:33.938804"}, {"name": "chrome (Browser Control)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=9222): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A116230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:37.976571"}, {"name": "mobile (Mobile Automation)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=9010): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A115960>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:42.015101"}, {"name": "playwright (Playwright)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=3001): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A1147F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:46.089449"}, {"name": "chart (Chart Service)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=3020): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A114A60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:50.142304"}, {"name": "xtts (Text-to-Speech)", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A117460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:54.198992"}, {"name": "Frontend-Backend Proxy", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=80): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A116E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:45:58.260536"}, {"name": "MCP Hub Integration", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=3000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A114EB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:46:02.327514"}, {"name": "XTTS Service Integration", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002123A1158A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:46:06.391816"}, {"name": "Data Volume Mount", "passed": false, "details": "Data directory not found", "timestamp": "2025-07-27T16:46:06.392387"}, {"name": "Environment Configuration", "passed": true, "details": "", "timestamp": "2025-07-27T16:46:06.393452"}, {"name": "Container Network", "passed": false, "details": "[WinError 2] The system cannot find the file specified", "timestamp": "2025-07-27T16:46:06.419709"}, {"name": "Logs Accessibility", "passed": false, "details": "[WinError 2] The system cannot find the file specified", "timestamp": "2025-07-27T16:46:06.425981"}, {"name": "Resource Monitoring", "passed": false, "details": "[WinError 2] The system cannot find the file specified", "timestamp": "2025-07-27T16:46:06.440541"}]}