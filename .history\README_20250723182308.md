# Agent Lee App

## Overview

Agent <PERSON> is a full-stack AI assistant with voice, vision, and workflow automation.

**Frontend:**

- All production HTML and assets live in `frontend_for_github_pages/` and are published to GitHub Pages.

**Backend:**

- All Python, Docker, and Fly.io deployment files remain in the main project root and `xtts/`.

## Folder Structure

```text
frontend_for_github_pages/
├── index.html               # Frontend (GitHub Pages)
├── assets/                  # Images/scripts for frontend
│   ├── agentlee_avatar.png
│   └── RWD_logo.png

xtts/                        # Python backend (Fly.io)
├── server.py
├── webhook_server.py
├── requirements.txt

Dockerfile                   # Docker config for backend
fly.toml                     # Fly.io deployment config
README.md                    # Project documentation
```

## Deployment

### Backend (Fly.io)

1. `fly auth login`
2. `fly launch --name agentlee-backend --no-deploy`
3. Edit `fly.toml` as needed
4. `fly deploy`

### Frontend (GitHub Pages)

1. Push `frontend_for_github_pages/index.html` and `assets/` to a GitHub repo
2. Enable GitHub Pages in repo settings

### Connect Frontend to Backend

Update all backend requests in `index.html` to use your Fly.io backend URL (e.g. [`https://agentlee-backend.fly.dev`](https://agentlee-backend.fly.dev)).

## API Keys

All sensitive keys are stored in `.env` and loaded automatically by Agent Lee.

## Features

- Voice and vision AI
- Chat, TTS, file upload, social media links
- Deep research and workflow automation

## Contact

Built by Leonard Lee. For support, email: [<EMAIL>](mailto:<EMAIL>)
