[CmdletBinding(SupportsShouldProcess=$true)]
param()

Write-Host "--- Starting Full Project Cleanup & Reorganization ---" -ForegroundColor Yellow
$root = $PSScriptRoot

# --- Define the new, clean folder structure ---
$docsPath = Join-Path -Path $root -ChildPath "docs"
$scriptsPath = Join-Path -Path $root -ChildPath "scripts"
$testsPath = Join-Path -Path $root -ChildPath "tests"

New-Item -ItemType Directory -Path $docsPath, $scriptsPath, $testsPath -Force -ErrorAction SilentlyContinue | Out-Null
Write-Host "Created 'docs', 'scripts', and 'tests' directories." -ForegroundColor Cyan

# --- Define what to delete and move ---
$filesToDelete = @(
    "agentlee.js",
    "agentlee-core.js", 
    "index.js",
    "test-livekit.html",
    "sw.js"
)

$filesToMove = @(
    @{ Pattern = "*.md"; Destination = $docsPath },
    @{ Pattern = "*.pdf"; Destination = $docsPath },
    @{ Pattern = "*.txt"; Destination = $docsPath },
    @{ Pattern = "*.bat"; Destination = $scriptsPath },
    @{ Pattern = "*.sh"; Destination = $scriptsPath },
    @{ Pattern = "*.ps1"; Destination = $scriptsPath },
    @{ Pattern = "test-*.py"; Destination = $testsPath },
    @{ Pattern = "test_*.py"; Destination = $testsPath },
    @{ Pattern = "test-*.js"; Destination = $testsPath },
    @{ Pattern = "*report.json"; Destination = $testsPath }
)

# --- Execution Phase ---
Write-Host "`nStep 1: Deleting obsolete and incompatible files..." -ForegroundColor Cyan

# Get all files excluding certain directories
$allFiles = Get-ChildItem -Path $root -Recurse -File | Where-Object { 
    $_.FullName -notmatch "\\node_modules\\" -and 
    $_.FullName -notmatch "\\.venv\\" -and 
    $_.FullName -notmatch "\\.git\\" -and
    $_.FullName -notmatch "\\coqui_env\\" -and
    $_.FullName -notmatch "\\__pycache__\\"
}

# Delete specific files
$allFiles | Where-Object { $_.Name -in $filesToDelete } | ForEach-Object {
    # Safety check: Do not delete the index.js if it's the only entry point
    if ($_.Name -eq "index.js" -and (Test-Path (Join-Path $_.Directory.FullName "index.tsx"))) {
        Write-Host "[SKIPPED Deletion] Ignoring '$($_.FullName)' because a sibling 'index.tsx' exists." -ForegroundColor Gray
    } else {
        if ($PSCmdlet.ShouldProcess($_.FullName, "Deleting obsolete file")) {
            Remove-Item $_.FullName -Force
            Write-Host "[DELETED] $($_.FullName)" -ForegroundColor Green
        }
    }
}

# Delete the node_modules directory to ensure a clean install later
$nodeModulesPath = Join-Path -Path $root -ChildPath "AgentLeeApp\frontend\agentlee3\node_modules"
if (Test-Path $nodeModulesPath) {
    if ($PSCmdlet.ShouldProcess($nodeModulesPath, "Deleting old node_modules folder for a clean slate")) {
        Remove-Item $nodeModulesPath -Recurse -Force
        Write-Host "[DELETED] Removed old node_modules folder." -ForegroundColor Green
    }
}

Write-Host "`nStep 2: Moving documentation, scripts, and tests to centralized folders..." -ForegroundColor Cyan

# Re-fetch file list after deletions
$remainingFiles = Get-ChildItem -Path $root -Recurse -File | Where-Object { 
    $_.FullName -notmatch "\\node_modules\\" -and 
    $_.FullName -notmatch "\\.venv\\" -and 
    $_.FullName -notmatch "\\.git\\" -and
    $_.FullName -notmatch "\\coqui_env\\" -and
    $_.FullName -notmatch "\\__pycache__\\"
}

foreach ($item in $filesToMove) {
    $foundFiles = $remainingFiles | Where-Object { $_.Name -like $item.Pattern }
    foreach ($file in $foundFiles) {
        # Exclude this script itself from being moved
        if ($file.Name -eq $MyInvocation.MyCommand.Name) { continue }
        
        # Skip files that are already in the target directories
        if ($file.FullName.StartsWith($docsPath) -or 
            $file.FullName.StartsWith($scriptsPath) -or 
            $file.FullName.StartsWith($testsPath)) { 
            continue 
        }
        
        # Skip essential files that should stay in their current locations
        $skipFiles = @("package.json", "README.md")
        if ($file.Directory.Name -eq "agentlee3" -and $file.Name -in $skipFiles) {
            Write-Host "[SKIPPED] Keeping $($file.Name) in frontend directory" -ForegroundColor Gray
            continue
        }
        
        $destination = $item.Destination
        if ($PSCmdlet.ShouldProcess($file.FullName, "Moving to '$destination'")) {
            try {
                Move-Item -Path $file.FullName -Destination $destination -Force -ErrorAction Stop
                Write-Host "[MOVED] $($file.Name) -> $($destination.Split('\')[-1])\" -ForegroundColor Green
            } catch {
                Write-Host "[ERROR] Could not move $($file.FullName). It may be in use or already exists." -ForegroundColor Red
            }
        }
    }
}

Write-Host "`nStep 3: Consolidating documentation..." -ForegroundColor Cyan

# Create a master README in docs folder that references all other documentation
$masterReadmePath = Join-Path -Path $docsPath -ChildPath "README.md"
$masterReadmeContent = @"
# Agent Lee Documentation

This directory contains all documentation for the Agent Lee project.

## Quick Start
- [Main README](README.md) - Project overview and setup instructions
- [Docker Setup](README.docker.md) - Docker deployment guide
- [Production Deployment](deploy-production.ps1) - Production deployment script

## Technical Documentation
- [Technical Analysis](TECHNICAL_ANALYSIS.md) - Detailed technical analysis
- [Engineering Documentation](ENGINEERING_DOCUMENTATION.md) - Engineering details
- [System Fixes Summary](SYSTEM_FIXES_SUMMARY.md) - Recent fixes and improvements

## Setup and Configuration
- [Setup Complete Guide](SETUP_COMPLETE.md) - Complete setup instructions
- [Living Docs Guide](LIVING_DOCS_GUIDE.md) - Documentation maintenance guide
- [Admin Dashboard](ADMIN_DASHBOARD_README.md) - Admin interface documentation

## Integration and Features
- [Agent Lee Complete Integration](AGENT_LEE_COMPLETE_INTEGRATION.md) - Integration guide
- [UI/UX Enhancements](AGENT_LEE_UI_UX_ENHANCEMENTS.md) - UI improvement details
- [Action Plan](ACTION_PLAN.md) - Development roadmap

## Project Structure
```
AgentLeeApp/
├── backend/         # Python Flask backend
├── frontend/        # React TypeScript frontend
│   └── agentlee3/   # Main React application
└── livekit/         # LiveKit server executable

docs/                # All documentation (this folder)
scripts/             # Deployment and utility scripts
tests/               # All test files
```

## Development Workflow
1. Backend: Python Flask with MCP tools integration
2. Frontend: React TypeScript with LiveKit for real-time communication
3. Docker: Multi-service architecture with nginx reverse proxy
4. Testing: Comprehensive test suite in the tests/ directory
"@

if ($PSCmdlet.ShouldProcess($masterReadmePath, "Creating master documentation index")) {
    Set-Content -Path $masterReadmePath -Value $masterReadmeContent -Force
    Write-Host "[CREATED] Master documentation index at docs/README.md" -ForegroundColor Green
}

Write-Host "`n--- Reorganization Complete ---" -ForegroundColor Yellow
Write-Host "Your project is now clean and organized!" -ForegroundColor Green

Write-Host "`n📁 New Project Structure:" -ForegroundColor Cyan
Write-Host "  📂 AgentLeeApp/          - Main application" -ForegroundColor White
Write-Host "    📂 backend/            - Python Flask backend" -ForegroundColor White  
Write-Host "    📂 frontend/agentlee3/ - React TypeScript frontend" -ForegroundColor White
Write-Host "    📂 livekit/            - LiveKit server" -ForegroundColor White
Write-Host "  📂 docs/                 - All documentation" -ForegroundColor White
Write-Host "  📂 scripts/              - Deployment scripts" -ForegroundColor White
Write-Host "  📂 tests/                - All test files" -ForegroundColor White

Write-Host "`n🔧 Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Navigate to: cd AgentLeeApp\frontend\agentlee3" -ForegroundColor White
Write-Host "  2. Install dependencies: npm install" -ForegroundColor White
Write-Host "  3. Start development: npm start" -ForegroundColor White
Write-Host "  4. Or run production: .\scripts\deploy-production.ps1" -ForegroundColor White

Write-Host "`n✅ Cleanup Summary:" -ForegroundColor Green
Write-Host "  • Removed obsolete JavaScript files" -ForegroundColor White
Write-Host "  • Organized documentation in docs/" -ForegroundColor White
Write-Host "  • Moved scripts to scripts/" -ForegroundColor White
Write-Host "  • Centralized tests in tests/" -ForegroundColor White
Write-Host "  • Created master documentation index" -ForegroundColor White
Write-Host "  • Preserved essential project structure" -ForegroundColor White