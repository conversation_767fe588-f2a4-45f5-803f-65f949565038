# <PERSON> App

## Overview
Agent <PERSON> is a full-stack AI assistant with voice, vision, and workflow automation. The frontend runs on GitHub Pages, and the backend (Python, TTS, APIs) is deployed on Fly.io using Docker.

## Folder Structure
```
AgentLeeApp/
├── index.html               # Frontend (GitHub Pages)
├── assets/                  # Images/scripts for frontend
│   ├── agentlee_avatar.png
│   └── RWD_logo.png
├── xtts/                    # Python backend (Fly.io)
│   ├── server.py
│   ├── webhook_server.py
│   └── requirements.txt
├── Dockerfile               # Docker config for backend
├── fly.toml                 # Fly.io deployment config
```

## Deployment
### Backend (Fly.io)
1. `fly auth login`
2. `fly launch --name agentlee-backend --no-deploy`
3. Edit `fly.toml` as needed
4. `fly deploy`

### Frontend (GitHub Pages)
1. Push `index.html` and `assets/` to a GitHub repo
2. Enable GitHub Pages in repo settings

### Connect Frontend to Backend
Update all backend requests in `index.html` to use your Fly.io backend URL (e.g. `https://agentlee-backend.fly.dev`).

## API Keys
All sensitive keys are stored in `.env` and loaded automatically by Agent <PERSON>.

## Features
- Voice and vision AI
- Chat, TTS, file upload, social media links
- Deep research and workflow automation

## Contact
Built by Leonard Lee. For support, email: <EMAIL>
