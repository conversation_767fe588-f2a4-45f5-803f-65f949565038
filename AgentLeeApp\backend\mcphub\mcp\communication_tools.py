"""
communication_tools.py - Unified communication tools for <PERSON> <PERSON>
Merges email_sender.py and calendar_event.py for email and calendar event handling.
Each tool is exposed as a function: run_email_sender(task, speaker) and run_calendar_event(task, speaker).
Unified input validation, sanitization, and error handling.
"""
from flask import jsonify
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import re
import logging
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("communication_tools")

def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def sanitize_input(text):
    if not isinstance(text, str):
        return ""
    dangerous_chars = ['<script', '</script', 'javascript:', 'data:', 'vbscript:']
    for char in dangerous_chars:
        text = text.replace(char, '')
    return text.strip()

def validate_task(task, speaker):
    if not isinstance(task, dict):
        raise ValueError("Task must be a dictionary")
    if not speaker or not isinstance(speaker, str):
        raise ValueError("Speaker must be a non-empty string")

def run_email_sender(task, speaker):
    try:
        validate_task(task, speaker)
        input_text = sanitize_input(task.get("input", ""))
        recipient = sanitize_input(task.get("to", ""))
        subject = sanitize_input(task.get("subject", f"Message from Agent Lee - {speaker}"))
        body = sanitize_input(task.get("body", input_text))
        logger.info(f"Processing email request from {speaker}")
        if not recipient:
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, input_text)
            if emails:
                recipient = emails[0]
                logger.info(f"Extracted recipient from input: {recipient}")
            else:
                logger.warning("No recipient email address found")
                return jsonify({"error": "No recipient email address provided."})
        if not validate_email(recipient):
            logger.warning(f"Invalid email format: {recipient}")
            return jsonify({"error": f"Invalid email format: {recipient}"})
        if not body:
            logger.warning("No email content provided")
            return jsonify({"error": "No email content provided."})
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        sender_email = os.getenv("SENDER_EMAIL", "<EMAIL>")
        sender_password = os.getenv("SENDER_PASSWORD", "")
        if not sender_password:
            logger.warning("No SMTP password configured, using fallback simulation")
            return jsonify({
                "status": "Email queued (simulated)",
                "to": recipient,
                "subject": subject,
                "method": "emailjs_fallback",
                "speaker": speaker,
                "tool": "email_sender"
            })
        if not validate_email(sender_email):
            logger.error(f"Invalid sender email configuration: {sender_email}")
            return jsonify({"error": "Invalid sender email configuration"})
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = recipient
        message["Subject"] = subject
        message.attach(MIMEText(body, "plain"))
        logger.info(f"Attempting to send email to {recipient}")
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)
        logger.info(f"Email sent successfully to {recipient}")
        return jsonify({
            "status": "Email sent successfully",
            "to": recipient,
            "subject": subject,
            "method": "smtp",
            "speaker": speaker,
            "tool": "email_sender"
        })
    except ValueError as ve:
        logger.warning(f"Validation error: {str(ve)}")
        return jsonify({"error": str(ve), "speaker": speaker, "tool": "email_sender"})
    except smtplib.SMTPAuthenticationError as auth_error:
        logger.error(f"SMTP authentication failed: {str(auth_error)}")
        return jsonify({"error": "Email authentication failed. Please check credentials.", "speaker": speaker, "tool": "email_sender"})
    except smtplib.SMTPException as smtp_error:
        logger.error(f"SMTP error: {str(smtp_error)}")
        return jsonify({"error": f"Email server error: {str(smtp_error)}", "speaker": speaker, "tool": "email_sender"})
    except Exception as e:
        logger.exception(f"Unexpected error sending email for {speaker}: {str(e)}")
        return jsonify({"error": f"Failed to send email: {str(e)}", "speaker": speaker, "tool": "email_sender"})

def parse_time_string(time_str, input_text=""):
    if not time_str:
        return None
    time_str = time_str.lower().strip()
    now = datetime.now()
    try:
        if time_str in ['today', 'now']:
            return now + timedelta(hours=1)
        elif time_str == 'tomorrow':
            return now + timedelta(days=1)
        elif time_str == 'next week':
            return now + timedelta(weeks=1)
        elif time_str == 'next month':
            return now + timedelta(days=30)
        time_patterns = [
            (r'(\d{1,2}):(\d{2})\s*(am|pm)', '%I:%M %p'),
            (r'(\d{1,2})\s*(am|pm)', '%I %p'),
            (r'(\d{1,2}):(\d{2})', '%H:%M'),
        ]
        for pattern, fmt in time_patterns:
            match = re.search(pattern, time_str)
            if match:
                if 'am' in time_str or 'pm' in time_str:
                    parsed_time = datetime.strptime(time_str, fmt)
                else:
                    parsed_time = datetime.strptime(time_str, fmt)
                return now.replace(hour=parsed_time.hour, minute=parsed_time.minute, second=0, microsecond=0)
        date_match = re.search(r'(\d{1,2})/(\d{1,2})(?:/(\d{4}))?', time_str)
        if date_match:
            month, day = int(date_match.group(1)), int(date_match.group(2))
            year = int(date_match.group(3)) if date_match.group(3) else now.year
            return datetime(year, month, day, now.hour, now.minute)
        weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        for i, day in enumerate(weekdays):
            if day in time_str:
                days_ahead = i - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return now + timedelta(days=days_ahead)
    except ValueError as e:
        logger.warning(f"Failed to parse time string '{time_str}': {str(e)}")
    return None

def run_calendar_event(task, speaker):
    try:
        validate_task(task, speaker)
        input_text = sanitize_input(task.get("input", ""))
        title = sanitize_input(task.get("title", ""))
        time_str = sanitize_input(task.get("time", ""))
        description = sanitize_input(task.get("description", input_text))
        logger.info(f"Calendar event request from {speaker}")
        if not title and not input_text:
            logger.warning("No event title or description provided")
            return jsonify({"error": "No event title or description provided."})
        if not title:
            sentences = input_text.split('.')
            title = sentences[0][:50] if sentences else input_text[:50]
            logger.info(f"Extracted title from input: {title}")
        if len(title) < 3:
            logger.warning(f"Title too short: {title}")
            return jsonify({"error": "Event title must be at least 3 characters long."})
        if not time_str:
            time_patterns = [
                r'(\d{1,2}:\d{2}\s*(?:AM|PM|am|pm))',
                r'(\d{1,2}\s*(?:AM|PM|am|pm))',
                r'(tomorrow|today|next week|next month)',
                r'(\d{1,2}/\d{1,2})',
                r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)'
            ]
            for pattern in time_patterns:
                match = re.search(pattern, input_text, re.IGNORECASE)
                if match:
                    time_str = match.group(1)
                    logger.info(f"Extracted time from input: {time_str}")
                    break
        event_time = parse_time_string(time_str, input_text)
        if not event_time:
            event_time = datetime.now() + timedelta(hours=1)
            logger.info("Using default time (1 hour from now)")
        if event_time < datetime.now() - timedelta(minutes=5):
            logger.warning(f"Event time is in the past: {event_time}")
            return jsonify({"error": "Event time cannot be in the past."})
        event_data = {
            "title": title,
            "description": description,
            "time": event_time.isoformat(),
            "time_formatted": event_time.strftime("%Y-%m-%d %H:%M"),
            "speaker": speaker,
            "created_at": datetime.now().isoformat(),
            "tool": "calendar_event"
        }
        logger.info(f"Event created successfully: {title} at {event_time}")
        return jsonify({
            "status": "Event created successfully",
            "event": event_data,
            "integration": "simulated",
            "speaker": speaker,
            "tool": "calendar_event"
        })
    except ValueError as ve:
        logger.warning(f"Validation error: {str(ve)}")
        return jsonify({"error": str(ve), "speaker": speaker, "tool": "calendar_event"})
    except Exception as e:
        logger.exception(f"Unexpected error creating calendar event for {speaker}: {str(e)}")
        return jsonify({"error": f"Failed to create calendar event: {str(e)}", "speaker": speaker, "tool": "calendar_event"})

# Entrypoint for tool registry compatibility
def run(task, speaker):
    tool = task.get("tool")
    if tool == "email_sender":
        return run_email_sender(task, speaker)
    elif tool == "calendar_event":
        return run_calendar_event(task, speaker)
    else:
        return jsonify({"error": f"Unknown communication tool: {tool}"})
