/**
 * Test script for Agent Lee Admin Dashboard
 * Verifies all components and functionality work correctly
 */

// Type declarations for browser APIs
declare global {
  interface Window {
    LiveKit?: any;
    livekit?: any;
    SpeechRecognition?: new () => SpeechRecognition;
    webkitSpeechRecognition?: new () => SpeechRecognition;
    AgentLeeCore?: {
      backendURL: string;
    };
    AgentLeeTests?: {
      runAllTests: () => Promise<number>;
      testComponentLoading: () => boolean;
      testLiveKitIntegration: () => boolean;
      testVoiceRecognition: () => boolean;
      testTextToSpeech: () => boolean;
      testBackendConnectivity: () => Promise<boolean>;
      testUIAnimations: () => boolean;
      testResponsiveDesign: () => boolean;
    };
  }
}

interface TestResult {
  name: string;
  passed: boolean;
}

console.log('🧪 Agent Lee Admin Dashboard Test Suite');
console.log('='.repeat(50));

// Test 1: Check if React and TypeScript components load
function testComponentLoading(): boolean {
    console.log('📦 Testing Component Loading...');
    
    const requiredElements: string[] = [
        '#root',
        '.glass',
        '.voice-wave'
    ];
    
    let passed = 0;
    requiredElements.forEach((selector: string) => {
        const element = document.querySelector(selector);
        if (element) {
            console.log(`✅ ${selector} found`);
            passed++;
        } else {
            console.log(`❌ ${selector} missing`);
        }
    });
    
    console.log(`Component Loading: ${passed}/${requiredElements.length} passed\n`);
    return passed === requiredElements.length;
}

// Test 2: Check LiveKit integration
function testLiveKitIntegration(): boolean {
    console.log('🎥 Testing LiveKit Integration...');
    
    try {
        // Check if LiveKit client is available
        if (typeof window.LiveKit !== 'undefined' || typeof window.livekit !== 'undefined') {
            console.log('✅ LiveKit client available');
        } else {
            console.log('⚠️ LiveKit client not loaded (expected in development)');
        }
        
        // Check for video elements
        const videoElements = document.querySelectorAll('video');
        console.log(`✅ Found ${videoElements.length} video elements`);
        
        // Check for voice controls
        const voiceControls = document.querySelectorAll('[class*="voice"], [class*="mic"]');
        console.log(`✅ Found ${voiceControls.length} voice control elements`);
        
        return true;
    } catch (error: any) {
        console.log(`❌ LiveKit integration error: ${error.message}`);
        return false;
    }
}

// Test 3: Check voice recognition setup
function testVoiceRecognition(): boolean {
    console.log('🎤 Testing Voice Recognition...');
    
    try {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (SpeechRecognition) {
            console.log('✅ Speech Recognition API available');
            
            const recognition = new SpeechRecognition();
            console.log('✅ Speech Recognition instance created');
            
            return true;
        } else {
            console.log('❌ Speech Recognition API not supported');
            return false;
        }
    } catch (error: any) {
        console.log(`❌ Voice recognition error: ${error.message}`);
        return false;
    }
}

// Test 4: Check text-to-speech
function testTextToSpeech(): boolean {
    console.log('🔊 Testing Text-to-Speech...');
    
    try {
        if ('speechSynthesis' in window) {
            console.log('✅ Speech Synthesis API available');
            
            const voices = speechSynthesis.getVoices();
            console.log(`✅ Found ${voices.length} available voices`);
            
            return true;
        } else {
            console.log('❌ Speech Synthesis API not supported');
            return false;
        }
    } catch (error: any) {
        console.log(`❌ Text-to-speech error: ${error.message}`);
        return false;
    }
}

// Test 5: Check backend connectivity
async function testBackendConnectivity(): Promise<boolean> {
    console.log('🌐 Testing Backend Connectivity...');
    
    try {
        const backendUrl = window.AgentLeeCore?.backendURL || 'http://localhost:8000';
        console.log(`Testing connection to: ${backendUrl}`);
        
        // Test health endpoint
        const response = await fetch(`${backendUrl}/health`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            console.log('✅ Backend health check passed');
            return true;
        } else {
            console.log(`⚠️ Backend responded with status: ${response.status}`);
            return false;
        }
    } catch (error: any) {
        console.log(`⚠️ Backend connectivity error: ${error.message}`);
        console.log('This is expected if backend is not running');
        return false;
    }
}

// Test 6: Check CSS animations and styles
function testUIAnimations(): boolean {
    console.log('🎨 Testing UI Animations...');
    
    try {
        // Check for CSS custom properties
        const rootStyles = getComputedStyle(document.documentElement);
        const hasCustomProps = rootStyles.getPropertyValue('--accent-blue');
        
        if (hasCustomProps) {
            console.log('✅ CSS custom properties loaded');
        } else {
            console.log('❌ CSS custom properties missing');
        }
        
        // Check for animation classes
        const animationClasses: string[] = [
            'voice-wave',
            'pulse-animation',
            'glass-effect',
            'gradient-text'
        ];
        
        let animationsFound = 0;
        animationClasses.forEach((className: string) => {
            const elements = document.getElementsByClassName(className);
            if (elements.length > 0) {
                console.log(`✅ Animation class .${className} found`);
                animationsFound++;
            }
        });
        
        console.log(`UI Animations: ${animationsFound}/${animationClasses.length} found\n`);
        return animationsFound > 0;
    } catch (error: any) {
        console.log(`❌ UI animation test error: ${error.message}`);
        return false;
    }
}

// Test 7: Check responsive design
function testResponsiveDesign(): boolean {
    console.log('📱 Testing Responsive Design...');
    
    try {
        const viewport = {
            width: window.innerWidth,
            height: window.innerHeight
        };
        
        console.log(`Current viewport: ${viewport.width}x${viewport.height}`);
        
        // Check for responsive classes
        const responsiveElements = document.querySelectorAll('[class*="md:"], [class*="lg:"], [class*="xl:"]');
        console.log(`✅ Found ${responsiveElements.length} responsive elements`);
        
        // Check grid layouts
        const gridElements = document.querySelectorAll('[class*="grid"]');
        console.log(`✅ Found ${gridElements.length} grid layout elements`);
        
        return true;
    } catch (error: any) {
        console.log(`❌ Responsive design test error: ${error.message}`);
        return false;
    }
}

// Run all tests
async function runAllTests(): Promise<number> {
    console.log('🚀 Starting Agent Lee Admin Dashboard Tests\n');
    
    const tests: Array<{ name: string; test: () => boolean | Promise<boolean> }> = [
        { name: 'Component Loading', test: testComponentLoading },
        { name: 'LiveKit Integration', test: testLiveKitIntegration },
        { name: 'Voice Recognition', test: testVoiceRecognition },
        { name: 'Text-to-Speech', test: testTextToSpeech },
        { name: 'Backend Connectivity', test: testBackendConnectivity },
        { name: 'UI Animations', test: testUIAnimations },
        { name: 'Responsive Design', test: testResponsiveDesign }
    ];
    
    let passed = 0;
    const results: TestResult[] = [];
    
    for (const { name, test } of tests) {
        try {
            const result = await test();
            results.push({ name, passed: result });
            if (result) passed++;
        } catch (error: any) {
            console.log(`❌ ${name} test failed: ${error.message}`);
            results.push({ name, passed: false });
        }
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST RESULTS');
    console.log('='.repeat(50));
    
    results.forEach(({ name, passed }: TestResult) => {
        const status = passed ? '✅ PASSED' : '❌ FAILED';
        console.log(`${name}: ${status}`);
    });
    
    console.log(`\nOverall: ${passed}/${tests.length} tests passed`);
    
    if (passed === tests.length) {
        console.log('🎉 All tests passed! Admin Dashboard is ready!');
    } else if (passed >= tests.length * 0.7) {
        console.log('⚠️ Most tests passed. Dashboard should work with minor issues.');
    } else {
        console.log('❌ Multiple test failures. Check configuration.');
    }
    
    return passed / tests.length;
}

// Auto-run tests when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runAllTests, 1000); // Wait for React to mount
    });
} else {
    setTimeout(runAllTests, 1000);
}

// Export for manual testing
window.AgentLeeTests = {
    runAllTests,
    testComponentLoading,
    testLiveKitIntegration,
    testVoiceRecognition,
    testTextToSpeech,
    testBackendConnectivity,
    testUIAnimations,
    testResponsiveDesign
};
