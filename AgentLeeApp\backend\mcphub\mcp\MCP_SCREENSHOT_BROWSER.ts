
// ...existing code...
import { MCPServer } from 'model-context-protocol';
import puppeteer from 'puppeteer';

export async function startServer(port = 3002) {
  const server = new MCPServer({ port });

  server.registerTool('screenshot', {
    title: 'Browser Screenshot',
    description: 'Capture screenshot of a URL or local file',
    parameters: {
      type: 'object',
      properties: {
        url: { type: 'string' },
        width: { type: 'integer', default: 1280 },
        height: { type: 'integer', default: 720 },
        fullPage: { type: 'boolean', default: false }
      },
      required: ['url']
    },
    returns: {
      type: 'object',
      properties: {
        image_base64: { type: 'string' }
      }
    }
  }, async ({ url, width, height, fullPage }) => {
    const browser = await puppeteer.launch({ args: ['--no-sandbox'] });
    const page = await browser.newPage();
    await page.setViewport({ width, height });
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });
    const buffer = await page.screenshot({ fullPage });
    await browser.close();
    const image_base64 = buffer.toString('base64');
    return { image_base64 };
  });

  await server.listen();
  console.log(`Screenshot MCP server listening on port ${port}`);
}
