
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from TTS.api import TTS
import tempfile
import os, requests

app = Flask(__name__)
CORS(app, origins=os.getenv('CORS_ORIGINS', '*'))

# Load XTTS model
tts = TTS(model_name="tts_models/multilingual/multi-dataset/xtts_v2", progress_bar=False, gpu=False)

@app.route("/api/tts", methods=["POST"])
def generate_tts():
    try:
        data = request.json
        text = data.get("text")
        speaker_wav = data.get("speaker_wav")
        language = data.get("language", "en")

        if not text or not speaker_wav:
            return jsonify({"error": "Missing required fields: text or speaker_wav"}), 400

        # Save speaker_wav to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_wav:
            tmp_wav.write(requests.get(speaker_wav).content)
            tmp_wav_path = tmp_wav.name

        # Generate speech
        output_path = tempfile.mktemp(suffix=".wav")
        tts.tts_to_file(text=text, speaker_wav=tmp_wav_path, language=language, file_path=output_path)

        return send_file(output_path, mimetype="audio/wav", as_attachment=True, download_name="output.wav")

    except Exception as e:
        return jsonify({"error": str(e)}), 500


# Gunicorn will import 'app' directly for production
if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.getenv('PORT', 5002)))

