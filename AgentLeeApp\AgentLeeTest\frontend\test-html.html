<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentLee HTML Validation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .test-item { margin: 10px 0; padding: 5px; }
        .status { font-weight: bold; }
        .pass .status { color: #28a745; }
        .fail .status { color: #dc3545; }
        .details { font-size: 0.9em; margin-top: 5px; }
    </style>
</head>
<body>
    <h1>🧪 AgentLee HTML Validation Test</h1>
    <p>This page tests the HTML structure and accessibility of the AgentLee application.</p>

    <div id="test-results">
        <div class="test-section">
            <h2>📋 Test Results</h2>
            <div id="results-container">
                <p>Running tests...</p>
            </div>
        </div>
    </div>

    <script>
        class HTMLValidator {
            constructor() {
                this.results = [];
                this.testMainApplication();
            }

            logTest(testName, passed, details = '') {
                const result = {
                    name: testName,
                    passed: passed,
                    details: details
                };
                this.results.push(result);
                this.displayResult(result);
            }

            displayResult(result) {
                const container = document.getElementById('results-container');
                const div = document.createElement('div');
                div.className = `test-item ${result.passed ? 'pass' : 'fail'}`;
                div.innerHTML = `
                    <div class="status">${result.passed ? '✅ PASS' : '❌ FAIL'}: ${result.name}</div>
                    ${result.details ? `<div class="details">${result.details}</div>` : ''}
                `;
                container.appendChild(div);
            }

            async testMainApplication() {
                try {
                    // Test if main application is accessible
                    const response = await fetch('http://localhost:80');
                    if (response.ok) {
                        const html = await response.text();
                        this.testHTMLStructure(html);
                        this.testAccessibility(html);
                        this.testJavaScriptIntegration(html);
                        this.testCSSIntegration(html);
                    } else {
                        this.logTest('Main Application Accessibility', false, `HTTP ${response.status}`);
                    }
                } catch (error) {
                    this.logTest('Main Application Accessibility', false, error.message);
                }
            }

            testHTMLStructure(html) {
                // Test basic HTML structure
                this.logTest('HTML Document Structure', 
                    html.includes('<html') && html.includes('</html>'),
                    'Complete HTML document structure');

                this.logTest('HTML Head Section', 
                    html.includes('<head') && html.includes('</head>'),
                    'Proper head section');

                this.logTest('HTML Body Section', 
                    html.includes('<body') && html.includes('</body>'),
                    'Proper body section');

                this.logTest('Viewport Meta Tag', 
                    html.includes('viewport'),
                    'Responsive viewport configuration');

                this.logTest('Character Encoding', 
                    html.includes('charset'),
                    'Proper character encoding declaration');
            }

            testAccessibility(html) {
                // Test accessibility features
                this.logTest('Language Declaration', 
                    html.includes('lang="en"'),
                    'Language attribute for screen readers');

                this.logTest('Chat Interface Elements', 
                    html.includes('chatMessages'),
                    'Chat messages container');

                this.logTest('Voice Button', 
                    html.includes('voiceBtn'),
                    'Voice recognition button');

                this.logTest('Message Input', 
                    html.includes('messageInput'),
                    'Text input for messages');

                this.logTest('Send Button', 
                    html.includes('sendBtn'),
                    'Send message button');

                this.logTest('Tools Panel', 
                    html.includes('tool-btn'),
                    'Tools panel buttons');
            }

            testJavaScriptIntegration(html) {
                // Test JavaScript integration
                this.logTest('AgentLee JavaScript', 
                    html.includes('agentlee.js'),
                    'Core JavaScript file included');

                this.logTest('Inline JavaScript', 
                    html.includes('<script'),
                    'JavaScript functionality present');

                this.logTest('Event Handlers', 
                    html.includes('addEventListener') || html.includes('onclick'),
                    'Event handling setup');
            }

            testCSSIntegration(html) {
                // Test CSS integration
                this.logTest('Tailwind CSS', 
                    html.includes('tailwindcss'),
                    'Tailwind CSS framework loaded');

                this.logTest('FontAwesome Icons', 
                    html.includes('fontawesome'),
                    'FontAwesome icons available');

                this.logTest('Custom CSS', 
                    html.includes('style.css'),
                    'Custom styling file included');

                this.logTest('Glass Effect Styling', 
                    html.includes('glass-effect'),
                    'Modern glass effect styling');
            }

            getSummary() {
                const passed = this.results.filter(r => r.passed).length;
                const total = this.results.length;
                const percentage = total > 0 ? (passed / total * 100).toFixed(1) : 0;
                
                return {
                    passed,
                    total,
                    percentage,
                    status: passed === total ? '✅ ALL TESTS PASSED' : 
                           passed > 0 ? '⚠️ PARTIAL SUCCESS' : '❌ ALL TESTS FAILED'
                };
            }
        }

        // Run tests when page loads
        window.addEventListener('load', () => {
            const validator = new HTMLValidator();
            
            // Display summary after a short delay
            setTimeout(() => {
                const summary = validator.getSummary();
                const summaryDiv = document.createElement('div');
                summaryDiv.className = `test-section ${summary.passed === summary.total ? 'pass' : 'fail'}`;
                summaryDiv.innerHTML = `
                    <h2>📊 Test Summary</h2>
                    <p><strong>${summary.status}</strong></p>
                    <p>Passed: ${summary.passed}/${summary.total} (${summary.percentage}%)</p>
                `;
                document.getElementById('test-results').appendChild(summaryDiv);
            }, 2000);
        });
    </script>
</body>
</html> 