export interface SearchResult {
  title: string;
  uri: string;
  snippet?: string;
}

export enum AgentMode {
  CEO = 'ceo',
  HipHop = 'hiphop', 
  Queen = 'queen'
}

export interface AgentState {
  mode: AgentMode;
  avatar: string;
  greeting: string;
  systemInstruction: string;
}

export interface LiveKitConfig {
  serverUrl: string;
  accessToken: string;
  roomName: string;
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'agent' | 'system';
  text: string;
  timestamp: string;
  content?: string; // For compatibility
}

export interface AgentResponse {
  text: string;
  searchResults: SearchResult[];
  error?: string;
  response?: string; // For compatibility
}
