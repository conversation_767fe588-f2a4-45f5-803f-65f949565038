# 🎤 Agent Lee — Google Voice Loop with Speaker Diarization (Phase 9)

import os
import io
import time
import threading
from memory_db import save_to_memory, save_session
from dispatcher import dispatch
from auto_task_generator import infer_task_tool_from_text

# 👥 Speaker map: speakerTag → name
speaker_map = {}

def recognize_stream():
    """Main voice recognition loop with Google Speech-to-Text and speaker diarization"""
    try:
        from google.cloud import speech_v1p1beta1 as speech
        # 🌐 Google STT Client
        client = speech.SpeechClient()
        mic_stream = get_live_mic_stream()
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=16000,
            language_code="en-US",
            enable_speaker_diarization=True,
            diarization_speaker_count=10,
            model="video",
            use_enhanced=True
        )
        streaming_config = speech.StreamingRecognitionConfig(
            config=config, 
            interim_results=False
        )

        print("🎤 Agent Lee is listening with diarization...")

        requests = (
            speech.StreamingRecognizeRequest(audio_content=chunk) 
            for chunk in mic_stream
        )
        responses = client.streaming_recognize(
            config=streaming_config, 
            requests=requests
        )

        for response in responses:
            for result in response.results:
                if not result.is_final:
                    continue
                for alt in result.alternatives:
                    words = alt.words
                    current_speaker = None
                    current_phrase = ""
                    for word_info in words:
                        if word_info.speaker_tag != current_speaker:
                            if current_speaker and current_phrase.strip():
                                speaker_name = resolve_speaker_name(current_speaker)
                                handle_phrase(speaker_name, current_phrase.strip())
                            current_speaker = word_info.speaker_tag
                            current_phrase = word_info.word
                        else:
                            current_phrase += " " + word_info.word
                    if current_speaker and current_phrase.strip():
                        speaker_name = resolve_speaker_name(current_speaker)
                        handle_phrase(speaker_name, current_phrase.strip())
    except ImportError:
        print("❌ Google Cloud Speech not available. Install: pip install google-cloud-speech")
        fallback_voice_loop()
    except Exception as e:
        print(f"❌ Voice loop failed: {e}")
        fallback_voice_loop()


def handle_phrase(speaker, text):
    """Process recognized speech phrase"""
    print(f"{speaker} 🎙️ {text}")
    
    # Save to memory and session
    save_to_memory(speaker, text)
    save_session(speaker, text)

    # Infer and execute task
    task = infer_task_tool_from_text(text)
    if task:
        print(f"🤖 Agent Lee executing: {task['tool']}")
        result = dispatch(task, speaker)
        print(f"✅ Result: {result}")
    else:
        # Default to LLM chat if no specific tool detected
        task = {"tool": "llm_chat", "input": text}
        dispatch(task, speaker)

def resolve_speaker_name(tag):
    """Resolve speaker tag to human name"""
    if tag not in speaker_map:
        # In production, you might want to use face recognition or pre-configured names
        name = f"Speaker_{tag}"
        speaker_map[tag] = name
        print(f"🧠 New speaker detected: {name}")
    return speaker_map[tag]

def get_live_mic_stream():
    """Get live microphone audio stream"""
    try:
        import pyaudio

        RATE = 16000
        CHUNK = int(RATE / 10)  # 100ms chunks
        
        audio_interface = pyaudio.PyAudio()
        stream = audio_interface.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=RATE,
            input=True,
            frames_per_buffer=CHUNK
        )

        def generator():
            while True:
                try:
                    yield stream.read(CHUNK)
                except Exception as e:
                    print(f"🎤 Audio stream error: {e}")
                    break

        return generator()
        
    except ImportError:
        print("❌ PyAudio not available. Install: pip install pyaudio")
        return iter([])

def fallback_voice_loop():
    """Fallback voice loop using basic speech recognition"""
    try:
        import speech_recognition as sr
        
        recognizer = sr.Recognizer()
        mic = sr.Microphone()
        
        print("🎤 Using fallback voice recognition (no diarization)")
        
        with mic as source:
            recognizer.adjust_for_ambient_noise(source)
        
        while True:
            try:
                with mic as source:
                    audio = recognizer.listen(source, timeout=1, phrase_time_limit=5)
                
                text = recognizer.recognize_google(audio)
                speaker = "VoiceUser"
                
                handle_phrase(speaker, text)
                
            except sr.WaitTimeoutError:
                pass
            except sr.UnknownValueError:
                pass
            except Exception as e:
                print(f"🎤 Fallback voice error: {e}")
                time.sleep(1)
                
    except ImportError:
        print("❌ No voice recognition available. Install speech_recognition and pyaudio")

def start_voice_loop():
    """Start the voice loop in a background thread"""
    voice_thread = threading.Thread(target=recognize_stream, daemon=True)
    voice_thread.start()
    print("🎤 Voice loop started in background")
    return voice_thread


# Place this at the end of the file, outside all functions
if __name__ == "__main__":
    import sys
    print("[DEPRECATED] This script is deprecated. Please use backend/voice_loop.py for unified CLI and Google STT support.")
    sys.exit(0)
