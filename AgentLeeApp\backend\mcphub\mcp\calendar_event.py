from flask import jsonify
from datetime import datetime, timedelta
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("calendar_event")

def sanitize_input(text):
    """Basic input sanitization"""
    if not isinstance(text, str):
        return ""
    # Remove potential injection characters and limit length
    text = re.sub(r'[<>"\';]', '', text)
    return text.strip()[:500]  # Limit to 500 chars

def validate_task(task, speaker):
    """Validate input task and speaker"""
    if not isinstance(task, dict):
        raise ValueError("Task must be a dictionary")
    if not speaker or not isinstance(speaker, str):
        raise ValueError("Speaker must be a non-empty string")

def parse_time_string(time_str, input_text=""):
    """Parse various time formats into datetime"""
    if not time_str:
        return None
    
    time_str = time_str.lower().strip()
    now = datetime.now()
    
    try:
        # Handle relative times
        if time_str in ['today', 'now']:
            return now + timedelta(hours=1)
        elif time_str == 'tomorrow':
            return now + timedelta(days=1)
        elif time_str == 'next week':
            return now + timedelta(weeks=1)
        elif time_str == 'next month':
            return now + timedelta(days=30)
        
        # Handle specific times like "3:00 PM"
        time_patterns = [
            (r'(\d{1,2}):(\d{2})\s*(am|pm)', '%I:%M %p'),
            (r'(\d{1,2})\s*(am|pm)', '%I %p'),
            (r'(\d{1,2}):(\d{2})', '%H:%M'),
        ]
        
        for pattern, fmt in time_patterns:
            match = re.search(pattern, time_str)
            if match:
                if 'am' in time_str or 'pm' in time_str:
                    parsed_time = datetime.strptime(time_str, fmt.replace('I', 'I').replace('M', 'M').replace('p', 'p'))
                else:
                    parsed_time = datetime.strptime(time_str, fmt)
                
                # Combine with today's date
                return now.replace(hour=parsed_time.hour, minute=parsed_time.minute, second=0, microsecond=0)
        
        # Handle date formats like "12/25" or "12/25/2024"
        date_match = re.search(r'(\d{1,2})/(\d{1,2})(?:/(\d{4}))?', time_str)
        if date_match:
            month, day = int(date_match.group(1)), int(date_match.group(2))
            year = int(date_match.group(3)) if date_match.group(3) else now.year
            return datetime(year, month, day, now.hour, now.minute)
        
        # Handle weekdays
        weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        for i, day in enumerate(weekdays):
            if day in time_str:
                days_ahead = i - now.weekday()
                if days_ahead <= 0:  # Target day already happened this week
                    days_ahead += 7
                return now + timedelta(days=days_ahead)
        
    except ValueError as e:
        logger.warning(f"Failed to parse time string '{time_str}': {str(e)}")
    
    return None

def run(task, speaker):
    """
    Enhanced calendar event creator with date/time parsing, validation, and logging
    """
    try:
        validate_task(task, speaker)
        
        input_text = sanitize_input(task.get("input", ""))
        title = sanitize_input(task.get("title", ""))
        time_str = sanitize_input(task.get("time", ""))
        description = sanitize_input(task.get("description", input_text))

        logger.info(f"Calendar event request from {speaker}")

        if not title and not input_text:
            logger.warning("No event title or description provided")
            return jsonify({"error": "No event title or description provided."})

        # Extract title from input if not provided
        if not title:
            # Simple title extraction - first sentence or first 50 chars
            sentences = input_text.split('.')
            title = sentences[0][:50] if sentences else input_text[:50]
            logger.info(f"Extracted title from input: {title}")

        # Validate title length
        if len(title) < 3:
            logger.warning(f"Title too short: {title}")
            return jsonify({"error": "Event title must be at least 3 characters long."})

        # Parse time from input text if not provided
        if not time_str:
            time_patterns = [
                r'(\d{1,2}:\d{2}\s*(?:AM|PM|am|pm))',
                r'(\d{1,2}\s*(?:AM|PM|am|pm))',
                r'(tomorrow|today|next week|next month)',
                r'(\d{1,2}/\d{1,2})',
                r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)'
            ]

            for pattern in time_patterns:
                match = re.search(pattern, input_text, re.IGNORECASE)
                if match:
                    time_str = match.group(1)
                    logger.info(f"Extracted time from input: {time_str}")
                    break

        # Parse the time string
        event_time = parse_time_string(time_str, input_text)
        
        # Default to 1 hour from now if no time found or parsing failed
        if not event_time:
            event_time = datetime.now() + timedelta(hours=1)
            logger.info("Using default time (1 hour from now)")

        # Validate event time is not in the past (with 5 minute buffer)
        if event_time < datetime.now() - timedelta(minutes=5):
            logger.warning(f"Event time is in the past: {event_time}")
            return jsonify({"error": "Event time cannot be in the past."})

        # Create event data
        event_data = {
            "title": title,
            "description": description,
            "time": event_time.isoformat(),
            "time_formatted": event_time.strftime("%Y-%m-%d %H:%M"),
            "speaker": speaker,
            "created_at": datetime.now().isoformat(),
            "tool": "calendar_event"
        }

        logger.info(f"Event created successfully: {title} at {event_time}")

        # Here you would integrate with actual calendar service
        # (Google Calendar API, Outlook, etc.)

        return jsonify({
            "status": "Event created successfully",
            "event": event_data,
            "integration": "simulated",  # Change to actual service
            "speaker": speaker,
            "tool": "calendar_event"
        })

    except ValueError as ve:
        logger.warning(f"Validation error: {str(ve)}")
        return jsonify({
            "error": str(ve),
            "speaker": speaker,
            "tool": "calendar_event"
        })
    except Exception as e:
        logger.exception(f"Unexpected error creating calendar event for {speaker}: {str(e)}")
        return jsonify({
            "error": f"Failed to create calendar event: {str(e)}",
            "speaker": speaker,
            "tool": "calendar_event"
        })

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    print(result.get_json().get("status", "Calendar event processing failed"))

if __name__ == "__main__":
    import sys
    print("[DEPRECATED] This script is deprecated. Please use backend/mcp/communication_tools.py for unified email and calendar tools.")
    sys.exit(0)