import React, { useState, useEffect, useRef } from 'react';
import { Room, RoomEvent } from 'livekit-client';
import config from '../config';
import { getLiveKitToken, sendMessageToAgent } from '../services/agentLeeService';

interface LiveKitControllerProps {
  onMessage?: (message: string) => void;
  room?: Room | null;
}

export const LiveKitController: React.FC<LiveKitControllerProps> = ({
  room,
  onMessage
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isMicEnabled, setIsMicEnabled] = useState(false);
  const [isCameraEnabled, setIsCameraEnabled] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const recognitionRef = useRef<any>(null);

  // Voice waveform animation state
  const [waveformBars, setWaveformBars] = useState([0, 0, 0, 0, 0]);

  // Attach media streams when room is available
  useEffect(() => {
    if (!room) return;

    const attachMedia = async () => {
      try {
        // Camera stream
        const videoTrack = room.localParticipant.videoTrackPublications.values().next().value?.track;
        if (localVideoRef.current && videoTrack) {
          videoTrack.attach(localVideoRef.current);
          setIsCameraEnabled(true);
        }

        // Audio stream
        const audioTrack = room.localParticipant.audioTrackPublications.values().next().value?.track;
        if (audioTrack) {
          setIsMicEnabled(true);
        }
      } catch (error) {
        console.error('Failed to attach media:', error);
      }
    };

    // Room event handlers
    room.on(RoomEvent.Connected, () => {
      console.log('LiveKit room connected');
      setIsConnected(true);
      attachMedia();
    });

    room.on(RoomEvent.Disconnected, () => {
      console.log('LiveKit room disconnected');
      setIsConnected(false);
    });

    // Visual analysis event
    room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
      if (onMessage) {
        onMessage(`VISUAL: ${participant.identity} - Video track subscribed`);
      }
    });

    // Audio analysis event
    room.on(RoomEvent.TrackPublished, (publication, participant) => {
      if (onMessage) {
        onMessage(`AUDIO: ${participant.identity} - ${publication.kind} track published`);
      }
    });

    if (room.state === 'connected') {
      setIsConnected(true);
      attachMedia();
    }

    initializeSpeechRecognition();

    return () => {
      recognitionRef.current?.stop();
    };
  }, [room, onMessage]);

  // Animate waveform when listening or speaking
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isListening || isSpeaking) {
      interval = setInterval(() => {
        setWaveformBars(prev => prev.map(() => Math.random() * 100));
      }, 150);
    } else {
      setWaveformBars([0, 0, 0, 0, 0]);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isListening, isSpeaking]);



  const enableCamera = async (roomInstance: Room) => {
    try {
      console.log('Requesting camera permissions...');
      
      // First request camera permission explicitly
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      console.log('Camera permission granted');
      
      // Stop the test stream
      stream.getTracks().forEach(track => track.stop());
      
      // Now enable camera through LiveKit
      await roomInstance.localParticipant.setCameraEnabled(true);
      setIsCameraEnabled(true);
      
      // Wait a bit for the track to be available
      setTimeout(() => {
        const videoTrack = roomInstance.localParticipant.videoTrackPublications.values().next().value?.track;
        if (videoTrack && localVideoRef.current) {
          videoTrack.attach(localVideoRef.current);
          console.log('Video track attached to local video element');
        }
      }, 1000);
      
    } catch (error) {
      console.error('Failed to enable camera:', error);
      alert('Camera access denied. Please allow camera access and refresh the page.');
    }
  };

  const enableMicrophone = async (roomInstance: Room) => {
    try {
      console.log('Requesting microphone permissions...');
      
      // First request microphone permission explicitly
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('Microphone permission granted');
      
      // Stop the test stream
      stream.getTracks().forEach(track => track.stop());
      
      // Now enable microphone through LiveKit
      await roomInstance.localParticipant.setMicrophoneEnabled(true);
      setIsMicEnabled(true);
      
      // Auto-start speech recognition when mic is enabled
      setTimeout(() => {
        if (recognitionRef.current) {
          try {
            recognitionRef.current.start();
            console.log('Speech recognition auto-started');
          } catch (e) {
            console.log('Speech recognition auto-start failed:', e);
          }
        }
      }, 1000);
      
    } catch (error) {
      console.error('Failed to enable microphone:', error);
      alert('Microphone access denied. Please allow microphone access and refresh the page.');
    }
  };

  const initializeSpeechRecognition = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      console.warn('SpeechRecognition API not supported.');
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true; // Enable interim results for better responsiveness
    recognition.lang = 'en-US';
    recognition.maxAlternatives = 1;

    recognition.onstart = () => {
      console.log('Speech recognition started');
      setIsListening(true);
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setIsListening(false);
      
      // Auto-restart on certain errors
      if (event.error === 'no-speech' || event.error === 'audio-capture') {
        setTimeout(() => {
          if (isMicEnabled && recognitionRef.current) {
            try {
              recognitionRef.current.start();
            } catch (e) {
              console.log('Recognition restart failed:', e);
            }
          }
        }, 1000);
      }
    };

    recognition.onend = () => {
      console.log('Speech recognition ended');
      setIsListening(false);
      
      // Auto-restart if microphone is still enabled
      if (isMicEnabled) {
        setTimeout(() => {
          try {
            recognitionRef.current?.start();
          } catch (e) {
            console.log('Recognition restart failed:', e);
          }
        }, 500);
      }
    };

    recognition.onresult = async (event) => {
      let transcript = '';
      let isFinal = false;
      
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          transcript += event.results[i][0].transcript;
          isFinal = true;
        }
      }
      
      if (isFinal && transcript.trim()) {
        console.log('Final recognized speech:', transcript.trim());
        await processVoiceCommand(transcript.trim());
      }
    };

    recognitionRef.current = recognition;
  };

  const processVoiceCommand = async (transcript: string) => {
    if (onMessage) {
      onMessage(`user: ${transcript}`);
    }

    try {
      // Send to backend using centralized service
      const agentResponse = await sendMessageToAgent(transcript, 'web_user');
      
      if (onMessage) {
        onMessage(`agent: ${agentResponse}`);
      }
      
      // Speak the response
      speakText(agentResponse);
    } catch (error) {
      console.error('Voice command processing error:', error);
    }
  };

  const speakText = (text: string) => {
    if (!('speechSynthesis' in window)) return;
    
    setIsSpeaking(true);
    
    // Cancel any ongoing speech
    speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 1;
    utterance.pitch = 1;
    utterance.volume = 1;
    utterance.lang = 'en-US'; // Force English language
    
    utterance.onend = () => {
      setIsSpeaking(false);
    };
    
    utterance.onerror = () => {
      setIsSpeaking(false);
    };
    
    // Wait for voices to load and select English voice
    const setVoice = () => {
      const voices = speechSynthesis.getVoices();
      const englishVoice = voices.find(voice => 
        voice.lang.startsWith('en') && 
        (voice.name.includes('Male') || voice.name.includes('David') || voice.name.includes('Mark') || voice.name.includes('Google') || voice.name.includes('Microsoft'))
      );
      if (englishVoice) {
        utterance.voice = englishVoice;
        console.log('Selected voice:', englishVoice.name, englishVoice.lang);
      }
      speechSynthesis.speak(utterance);
    };
    
    if (speechSynthesis.getVoices().length > 0) {
      setVoice();
    } else {
      speechSynthesis.onvoiceschanged = setVoice;
    }
  };

  const toggleMicrophone = async () => {
    if (!room) return;
    
    try {
      if (isMicEnabled) {
        await room.localParticipant.setMicrophoneEnabled(false);
        setIsMicEnabled(false);
        if (recognitionRef.current) {
          recognitionRef.current.stop();
        }
      } else {
        await room.localParticipant.setMicrophoneEnabled(true);
        setIsMicEnabled(true);
        if (recognitionRef.current) {
          recognitionRef.current.start();
        }
      }
    } catch (error) {
      console.error('Error toggling microphone:', error);
    }
  };

  const toggleCamera = async () => {
    if (!room) return;
    
    try {
      if (isCameraEnabled) {
        await room.localParticipant.setCameraEnabled(false);
        setIsCameraEnabled(false);
      } else {
        await room.localParticipant.setCameraEnabled(true);
        setIsCameraEnabled(true);
      }
    } catch (error) {
      console.error('Error toggling camera:', error);
    }
  };

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header with Agent Lee Avatar and Waveform */}
      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <img 
              src="./assets/agentlee_avatar.png" 
              alt="Agent Lee" 
              className="w-12 h-12 rounded-full border-2 border-blue-500"
            />
            <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-gray-800 ${
              isConnected ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-white">Agent Lee</h3>
            <p className="text-sm text-gray-400">
              {isConnected ? 'Connected' : 'Connecting...'}
            </p>
          </div>
        </div>

        {/* Voice Waveform Visualizer */}
        <div className="flex items-center space-x-1">
          {waveformBars.map((height, index) => (
            <div
              key={index}
              className="waveform-bar"
              style={{ '--bar-height': `${Math.max(4, height * 0.3)}px` } as React.CSSProperties}
            />
          ))}
        </div>
      </div>

      {/* Main Video Row */}
      <div className="flex-1 grid grid-cols-3 gap-4">
        {/* Your Camera Feed */}
        <div className="relative bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
          <div className="absolute top-2 left-2 z-10 bg-black/50 px-2 py-1 rounded text-xs text-white">
            You
          </div>
          <video
            ref={localVideoRef}
            autoPlay
            muted
            playsInline
            className="w-full h-full object-cover"
          />
          {!isCameraEnabled && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
              <div className="text-center text-gray-400">
                <div className="text-4xl mb-2">📷</div>
                <p>Camera Off</p>
              </div>
            </div>
          )}
        </div>

        {/* Agent Lee's View */}
        <div className="relative bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
          <div className="absolute top-2 left-2 z-10 bg-black/50 px-2 py-1 rounded text-xs text-white">
            Agent Lee
          </div>
          {isConnected ? (
            <video
              ref={remoteVideoRef}
              autoPlay
              playsInline
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-900/20 to-purple-900/20">
              <div className="text-center text-gray-400">
                <img
                  src="./assets/agentlee_avatar.png"
                  alt="Agent Lee"
                  className="w-20 h-20 rounded-full mx-auto mb-4 border-2 border-blue-500"
                />
                <p>Waiting for Agent Lee...</p>
              </div>
            </div>
          )}
        </div>

        {/* Voice Control Panel */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-4">
          <h4 className="text-white font-semibold mb-4">Voice Controls</h4>
          
          <div className="space-y-3">
            <button
              onClick={toggleMicrophone}
              className={`w-full p-3 rounded-lg font-medium transition-all ${
                isMicEnabled 
                  ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                  : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <span>{isMicEnabled ? '🎤' : '🔇'}</span>
                <span>{isMicEnabled ? 'Mic On' : 'Mic Off'}</span>
              </div>
            </button>

            <button
              onClick={isListening ? stopListening : startListening}
              className={`w-full p-3 rounded-lg font-medium transition-all ${
                isListening 
                  ? 'bg-green-600 hover:bg-green-700 text-white' 
                  : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <span>{isListening ? '🔴' : '▶️'}</span>
                <span>{isListening ? 'Listening...' : 'Start Talking'}</span>
              </div>
            </button>

            <button
              onClick={toggleCamera}
              className={`w-full p-3 rounded-lg font-medium transition-all ${
                isCameraEnabled 
                  ? 'bg-purple-600 hover:bg-purple-700 text-white' 
                  : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <span>{isCameraEnabled ? '📹' : '📷'}</span>
                <span>{isCameraEnabled ? 'Camera On' : 'Camera Off'}</span>
              </div>
            </button>

            <button
              onClick={() => speakText("Hello! I'm Agent Lee. Voice test successful. I'm speaking in English.")}
              className="w-full p-3 rounded-lg font-medium transition-all bg-green-600 hover:bg-green-700 text-white"
            >
              <div className="flex items-center justify-center space-x-2">
                <span>🔊</span>
                <span>Test Voice</span>
              </div>
            </button>
          </div>

          {/* Status Indicators */}
          <div className="mt-4 pt-4 border-t border-gray-700">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Connection:</span>
                <span className={isConnected ? 'text-green-400' : 'text-red-400'}>
                  {isConnected ? 'Online' : 'Offline'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Participants:</span>
                <span className="text-blue-400">{isConnected ? '2' : '1'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <span className={`${
                  isListening ? 'text-green-400' : 
                  isSpeaking ? 'text-blue-400' : 'text-gray-400'
                }`}>
                  {isListening ? 'Listening' : isSpeaking ? 'Speaking' : 'Ready'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};