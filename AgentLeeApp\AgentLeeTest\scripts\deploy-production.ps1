#!/usr/bin/env pwsh
# Production Deployment Script for <PERSON>
# Fully automated deployment with zero manual configuration

Write-Host "🚀 Agent Lee Production Deployment" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Set production environment variables
$env:FLASK_ENV = "production"
$env:FLASK_DEBUG = "false"
$env:PORT = "8000"
$env:CORS_ORIGINS = "*"

# Generate secure tokens if not set
if (-not $env:JWT_SECRET) {
    $env:JWT_SECRET = -join ((1..32) | ForEach-Object { [char]((65..90) + (97..122) + (48..57) | Get-Random) })
    Write-Host "✅ Generated JWT secret" -ForegroundColor Green
}

if (-not $env:AGENT_LEE_API_TOKEN) {
    $env:AGENT_LEE_API_TOKEN = -join ((1..24) | ForEach-Object { [char]((65..90) + (97..122) + (48..57) | Get-Random) })
    Write-Host "✅ Generated API token" -ForegroundColor Green
}

# Set default API keys (production should override these)
if (-not $env:GOOGLE_API_KEY) {
    $env:GOOGLE_API_KEY = "AIzaSyBpJiw1ym8tTPGI4Mi6a00qSPiudtgoDXo"
    Write-Host "✅ Using default Google API key" -ForegroundColor Yellow
}

# Install backend dependencies
Write-Host "📦 Installing backend dependencies..." -ForegroundColor Blue
Set-Location "AgentLeeApp\backend"
try {
    pip install Flask==2.3.3 flask-cors==4.0.0 PyJWT==2.8.0 requests==2.31.0 python-dotenv==1.0.0 gunicorn==21.2.0
    Write-Host "✅ Backend dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install backend dependencies: $_" -ForegroundColor Red
    exit 1
}

# Start LiveKit server first
Write-Host "🎥 Starting LiveKit server..." -ForegroundColor Blue
$livekitJob = Start-Job -ScriptBlock {
    Set-Location "d:\LLE+DOCKER\AgentLeeApp\livekit"
    .\livekit-server.exe --dev --bind 0.0.0.0 --port 7880
}

# Wait for LiveKit to start
Start-Sleep -Seconds 3

# Start backend server
Write-Host "🔧 Starting backend server..." -ForegroundColor Blue
$backendJob = Start-Job -ScriptBlock {
    Set-Location "d:\LLE+DOCKER\AgentLeeApp\backend"
    $env:FLASK_ENV = "production"
    $env:PORT = "8000"
    $env:LIVEKIT_SERVER_URL = "ws://localhost:7880"
    python server.py
}

# Wait for backend to start
Start-Sleep -Seconds 3

# Test backend health
Write-Host "🏥 Testing backend health..." -ForegroundColor Blue
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8000/health" -TimeoutSec 10
    if ($healthResponse.status -eq "OK") {
        Write-Host "✅ Backend is healthy" -ForegroundColor Green
    } else {
        throw "Backend health check failed"
    }
} catch {
    Write-Host "❌ Backend health check failed: $_" -ForegroundColor Red
    Stop-Job $backendJob
    exit 1
}

# Start frontend server
Write-Host "🌐 Starting frontend server..." -ForegroundColor Blue
Set-Location "../../AgentLeeApp/frontend/agentlee3"
$frontendJob = Start-Job -ScriptBlock {
    Set-Location "d:\LLE+DOCKER\AgentLeeApp\frontend\agentlee3"
    python server.py 8081
}

# Wait for frontend to start
Start-Sleep -Seconds 2

# Test frontend
Write-Host "🖥️ Testing frontend..." -ForegroundColor Blue
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:8081" -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend is serving" -ForegroundColor Green
    } else {
        throw "Frontend test failed"
    }
} catch {
    Write-Host "❌ Frontend test failed: $_" -ForegroundColor Red
    Stop-Job $backendJob
    Stop-Job $frontendJob
    exit 1
}

# Run comprehensive tests
Write-Host "🧪 Running comprehensive tests..." -ForegroundColor Blue

# Backend tests
Write-Host "Testing backend..." -ForegroundColor Yellow
Set-Location "d:\LLE+DOCKER\tests"
try {
    $testResult = python test_backend.py http://localhost:8000
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend tests passed" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Some backend tests failed, but continuing..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Backend test execution failed: $_" -ForegroundColor Yellow
}

# Display deployment summary
Write-Host "`n🎉 Agent Lee Deployment Complete!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "📱 Frontend URL: http://localhost:8081" -ForegroundColor Cyan
Write-Host "📡 Backend URL: http://localhost:8000" -ForegroundColor Cyan
Write-Host "🧪 Run frontend tests: http://localhost:8081?autotest=true" -ForegroundColor Cyan
Write-Host "🔑 API Token: $env:AGENT_LEE_API_TOKEN" -ForegroundColor Yellow
Write-Host "🔐 JWT Secret: [HIDDEN]" -ForegroundColor Yellow

Write-Host "`n🚀 Features Available:" -ForegroundColor Magenta
Write-Host "  ✅ Voice Recognition & Synthesis" -ForegroundColor White
Write-Host "  ✅ LiveKit Video Conferencing" -ForegroundColor White
Write-Host "  ✅ MCP Tool Integration (21 tools)" -ForegroundColor White
Write-Host "  ✅ Weather Information" -ForegroundColor White
Write-Host "  ✅ Screenshot Capture" -ForegroundColor White
Write-Host "  ✅ LLM Chat (Gemini)" -ForegroundColor White
Write-Host "  ✅ Memory System" -ForegroundColor White
Write-Host "  ✅ User Authentication" -ForegroundColor White
Write-Host "  ✅ Real-time Communication" -ForegroundColor White

Write-Host "`n📋 Quick Start:" -ForegroundColor Cyan
Write-Host "  1. Open http://localhost:8081 in your browser" -ForegroundColor White
Write-Host "  2. Login with any username/password" -ForegroundColor White
Write-Host "  3. Allow microphone access when prompted" -ForegroundColor White
Write-Host "  4. Start talking to Agent Lee!" -ForegroundColor White

Write-Host "`n🎯 Voice Commands to Try:" -ForegroundColor Cyan
Write-Host "  • 'What's the weather like today?'" -ForegroundColor White
Write-Host "  • 'Take a screenshot'" -ForegroundColor White
Write-Host "  • 'Hello Agent Lee, how are you?'" -ForegroundColor White

# Keep servers running
Write-Host "`n⏳ Servers are running. Press Ctrl+C to stop." -ForegroundColor Yellow
try {
    while ($true) {
        Start-Sleep -Seconds 5
        
        # Check if jobs are still running
        if ($backendJob.State -ne "Running") {
            Write-Host "❌ Backend server stopped unexpectedly" -ForegroundColor Red
            break
        }
        if ($frontendJob.State -ne "Running") {
            Write-Host "❌ Frontend server stopped unexpectedly" -ForegroundColor Red
            break
        }
    }
} finally {
    Write-Host "`n🛑 Stopping servers..." -ForegroundColor Yellow
    Stop-Job $backendJob -ErrorAction SilentlyContinue
    Stop-Job $frontendJob -ErrorAction SilentlyContinue
    Remove-Job $backendJob -ErrorAction SilentlyContinue
    Remove-Job $frontendJob -ErrorAction SilentlyContinue
    Write-Host "✅ Cleanup complete" -ForegroundColor Green
}