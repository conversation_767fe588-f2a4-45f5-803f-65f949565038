import { AgentResponse, SearchResult } from '../types';

interface GeminiServiceConfig {
  apiKey?: string;
  baseUrl?: string;
}

class GeminiService {
  private config: GeminiServiceConfig;

  constructor(config: GeminiServiceConfig = {}) {
    this.config = {
      apiKey: config.apiKey || process.env.REACT_APP_GEMINI_API_KEY,
      baseUrl: config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta'
    };
  }

  async fetchAgentResponse(message: string): Promise<AgentResponse> {
    try {
      // For now, return a mock response
      // In production, this would make actual API calls to Gemini/Agent <PERSON> backend
      
      const mockSearchResults: SearchResult[] = [
        {
          title: 'Agent Lee Documentation',
          uri: 'https://agentlee.ai/docs',
          snippet: 'Comprehensive guide to using Agent Lee AI assistant'
        },
        {
          title: 'AI Assistant Best Practices',
          uri: 'https://example.com/ai-practices',
          snippet: 'Learn how to effectively interact with AI assistants'
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200));

      const responses = [
        `I understand you're asking about "${message}". Let me provide you with a comprehensive response based on my knowledge.`,
        `That's a great question about "${message}". Here's what I can tell you from my analysis.`,
        `Regarding "${message}", I've processed your query and here's my detailed response.`,
        `Thank you for asking about "${message}". Based on my understanding, here's what I recommend.`
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)];

      return {
        text: randomResponse,
        response: randomResponse, // For compatibility
        searchResults: mockSearchResults
      };

    } catch (error) {
      console.error('Error fetching agent response:', error);
      
      return {
        text: 'I apologize, but I encountered an error processing your request. Please try again.',
        response: 'I apologize, but I encountered an error processing your request. Please try again.',
        searchResults: [],
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async searchWeb(query: string): Promise<SearchResult[]> {
    try {
      // Mock web search results
      // In production, this would integrate with actual search APIs
      
      await new Promise(resolve => setTimeout(resolve, 500));

      return [
        {
          title: `Search results for: ${query}`,
          uri: `https://search.example.com/q=${encodeURIComponent(query)}`,
          snippet: `Here are the top results for your search query: "${query}"`
        },
        {
          title: 'Related Information',
          uri: 'https://example.com/related',
          snippet: 'Additional context and related information for your query'
        }
      ];

    } catch (error) {
      console.error('Error searching web:', error);
      return [];
    }
  }

  async generateSpeech(text: string): Promise<void> {
    try {
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 0.8;
        
        // Try to use a more natural voice if available
        const voices = speechSynthesis.getVoices();
        const preferredVoice = voices.find(voice => 
          voice.name.includes('Google') || 
          voice.name.includes('Microsoft') ||
          voice.lang.startsWith('en')
        );
        
        if (preferredVoice) {
          utterance.voice = preferredVoice;
        }

        speechSynthesis.speak(utterance);
      } else {
        console.warn('Speech synthesis not supported in this browser');
      }
    } catch (error) {
      console.error('Error generating speech:', error);
    }
  }
}

// Export singleton instance
export const geminiService = new GeminiService();

// Export the main function for backward compatibility
export const fetchAgentResponse = (message: string): Promise<AgentResponse> => {
  return geminiService.fetchAgentResponse(message);
};
