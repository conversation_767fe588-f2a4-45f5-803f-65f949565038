{"web_user": {"introduced": true, "last_interaction": "2025-07-30T09:47:53.570323", "session_count": 0, "conversation_context": [{"message": "How's it going agently? How you doing?", "response": "🎤 Yo. I'm <PERSON>. And this ain't just a setup. It's the beginning of a real rhythm I build *around you*.", "timestamp": "2025-07-30T09:47:53.569323"}], "personality_mode": "auto", "user_name": null, "user_city": null, "user_passion": null, "emotional_state": "neutral", "trust_level": 100, "memory_vault": {}, "habit_tracking": {}, "long_term_goals": [], "emotional_anchors": [], "skill_preferences": [], "communication_style": "adaptive"}, "test_user": {"introduced": true, "last_interaction": "2025-07-30T12:01:53.476965", "session_count": 0, "conversation_context": [{"message": "Hello Agent <PERSON>", "response": "🎤 Yo. I'm <PERSON>. And this ain't just a setup. It's the beginning of a real rhythm I build *around you*.", "timestamp": "2025-07-30T11:06:35.759980"}, {"message": "Hello <PERSON>, this is a test", "response": "👂 Got it. Anything else on your mind? Or you want me to handle that first?", "timestamp": "2025-07-30T12:01:53.476965"}], "personality_mode": "auto", "user_name": null, "user_city": null, "user_passion": null, "emotional_state": "neutral", "trust_level": 100, "memory_vault": {}, "habit_tracking": {}, "long_term_goals": [], "emotional_anchors": [], "skill_preferences": [], "communication_style": "adaptive"}}