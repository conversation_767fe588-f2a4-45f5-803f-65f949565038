<#
.SYNOPSIS
    Consolidates <PERSON>'s core logic into modern React hooks and services,
    and removes the old, redundant JavaScript files.

.DESCRIPTION
    This script performs a critical refactoring step by:
    1. Creating new directories: 'src/hooks' and 'src/services'.
    2. Writing new, refactored code into 'useAgentLee.ts' and 'agentLeeService.ts'.
    3. Deleting the now-obsolete files: 'agentlee.js', 'agentlee-core.js', and 'sw.js'.
#>
[CmdletBinding(SupportsShouldProcess=$true)]
param()

Write-Host "--- Consolidating Agent Lee Core Logic ---" -ForegroundColor Yellow

$projectRoot = $PSScriptRoot
$srcPath = Join-Path -Path $projectRoot -ChildPath "src"

# --- 1. Define the new, corrected code content ---

$agentLeeServiceContent = @"
//
// --- This is the content for the new file: src/services/agentLeeService.ts ---
//

// Centralized configuration for backend URL
const backendUrl = (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1'))
  ? 'http://localhost:8000'
  : 'https://agentlee.fly.dev';

/**
 * Fetches a LiveKit token from the backend.
 * @param identity The identity of the user for the LiveKit room.
 * @param room The name of the room to join.
 * @returns A promise that resolves with the token and server URL.
 */
export const getLiveKitToken = async (identity: string = 'user', room: string = 'agent-lee-room') => {
  try {
    const response = await fetch(`${backendUrl}/api/livekit/token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ identity, room })
    });

    if (!response.ok) {
      throw new Error(`Failed to get LiveKit token: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error('LiveKit Token Error:', error);
    // Return details for a local dev server as a fallback
    return { token: null, server_url: 'ws://localhost:7880' };
  }
};

/**
 * Sends a message to the Agent Lee backend chat endpoint.
 * @param message The text message to send.
 * @param userId The ID of the user sending the message.
 * @returns A promise that resolves with the agent's response text.
 */
export const sendMessageToAgent = async (message: string, userId: string = 'user'): Promise<string> => {
  try {
    const response = await fetch(`${backendUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: message,
        user_id: userId,
        conversation_type: 'voice' // Or could be dynamic
      })
    });

    if (!response.ok) {
      throw new Error(`Backend chat API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.response || "I received that, but I'm not sure how to respond.";
  } catch (error) {
    console.error('Send Message Error:', error);
    return "I'm having trouble connecting to my core systems right now.";
  }
};
"@

$useAgentLeeHookContent = @"
//
// --- This is the content for the new file: src/hooks/useAgentLee.ts ---
//

import { useState, useCallback } from 'react';
import { sendMessageToAgent } from '../services/agentLeeService';

export interface ChatMessage {
  id: string;
  sender: 'user' | 'agent' | 'system';
  text: string;
  timestamp: string;
}

/**
 * A custom hook to manage the state and logic for Agent Lee.
 */
export const useAgentLee = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isSpeaking, setIsSpeaking] = useState(false);

  /**
   * Speaks the given text using the browser's TTS engine.
   */
  const speak = useCallback((text: string) => {
    if (!text || !window.speechSynthesis) {
      console.warn('Speech synthesis not available or no text provided.');
      return;
    }

    setIsSpeaking(true);
    window.speechSynthesis.cancel(); // Stop any previous speech

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1.0;

    // Optional: Select a preferred voice
    const voices = window.speechSynthesis.getVoices();
    const preferredVoice = voices.find(v => v.name.includes('Google') && v.lang.startsWith('en'));
    if (preferredVoice) {
      utterance.voice = preferredVoice;
    }

    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = () => {
      console.error('An error occurred during speech synthesis.');
      setIsSpeaking(false);
    };

    window.speechSynthesis.speak(utterance);
  }, []);

  /**
   * Sends a user's message, gets a response from the agent, and updates the chat.
   */
  const sendMessage = useCallback(async (messageText: string) => {
    // Add user message to state
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'user',
      text: messageText,
      timestamp: new Date().toLocaleTimeString(),
    };
    setMessages(prev => [...prev, userMessage]);

    // Get agent's response
    const agentResponseText = await sendMessageToAgent(messageText);

    // Add agent message to state
    const agentMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      sender: 'agent',
      text: agentResponseText,
      timestamp: new Date().toLocaleTimeString(),
    };
    setMessages(prev => [...prev, agentMessage]);

    // Speak the response
    speak(agentResponseText);
  }, [speak]);

  return {
    messages,
    isSpeaking,
    sendMessage,
    speak,
  };
};
"@

# --- 2. Create new directories and files ---

$newDirs = @("hooks", "services")
foreach ($dir in $newDirs) {
    $fullPath = Join-Path -Path $srcPath -ChildPath $dir
    if (-not (Test-Path $fullPath)) {
        if ($PSCmdlet.ShouldProcess($fullPath, "Creating directory")) {
            New-Item -Path $fullPath -ItemType Directory | Out-Null
            Write-Host "[CREATED] Directory: $fullPath" -ForegroundColor Green
        }
    }
}

$newFiles = @{
    (Join-Path $srcPath "services\agentLeeService.ts") = $agentLeeServiceContent
    (Join-Path $srcPath "hooks\useAgentLee.ts") = $useAgentLeeHookContent
}

foreach ($entry in $newFiles.GetEnumerator()) {
    if ($PSCmdlet.ShouldProcess($entry.Name, "Creating new logic file")) {
        Set-Content -Path $entry.Name -Value $entry.Value
        Write-Host "[CREATED] File: $($entry.Name)" -ForegroundColor Green
    }
}

# --- 3. Delete the old files ---

$filesToDelete = @(
    "agentlee.js",
    "agentlee-core.js",
    "sw.js"
)

foreach ($fileName in $filesToDelete) {
    $filePath = Join-Path -Path $projectRoot -ChildPath $fileName
    if (Test-Path $filePath) {
        if ($PSCmdlet.ShouldProcess($filePath, "Deleting obsolete file")) {
            Remove-Item -Path $filePath -Force
            Write-Host "[DELETED] Obsolete file: $filePath" -ForegroundColor Green
        }
    } else {
        Write-Host "[SKIPPED] Old file not found: $fileName" -ForegroundColor Gray
    }
}

Write-Host "--- Consolidation Complete ---" -ForegroundColor Yellow
Write-Host "Next Step: Update your App.tsx to use the new 'useAgentLee' hook." -ForegroundColor Cyan