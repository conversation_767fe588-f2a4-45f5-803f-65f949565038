@echo off
echo 🚀 Starting Agent Lee in Production Mode...

REM Set production environment
set FLASK_ENV=production
set FLASK_DEBUG=false

REM Copy production environment file
if exist .env.production (
    copy .env.production .env
    echo ✅ Production environment configured
) else (
    echo ⚠️  Warning: .env.production not found, using default settings
)

REM Start backend
echo 📡 Starting backend server...
cd backend
start "Agent Lee Backend" python server.py

REM Wait for backend to start
timeout /t 3 /nobreak > nul

REM Start frontend (simple HTTP server for production)
echo 🌐 Starting frontend server...
cd ..\frontend\agentlee3
start "Agent <PERSON> Frontend" python -m http.server 8080

echo ✅ Agent <PERSON> is now running in production mode!
echo 📱 Frontend: http://localhost:8080
echo 📡 Backend: http://localhost:5000
echo 🧪 Run tests: http://localhost:8080?autotest=true

REM Keep window open
pause