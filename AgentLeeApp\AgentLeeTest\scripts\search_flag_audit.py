#!/usr/bin/env python3
"""
Audit script to scan for flags like TODO, FIXME, HACK, XXX, and custom flags in HTML, JS, and Python files.
Outputs a report and exits with nonzero code if any are found.
"""
import os
import sys
import re

# You can add more flags here
FLAGS = [r'TODO', r'FIXME', r'HACK', r'XXX', r'BUG', r'\bDEBUG\b', r'\bTEMP\b']
EXTENSIONS = ['.py', '.js', '.html']

flag_regex = re.compile(r'(' + '|'.join(FLAGS) + r')', re.IGNORECASE)

results = []

for root, dirs, files in os.walk('.'):
    for fname in files:
        if any(fname.endswith(ext) for ext in EXTENSIONS):
            path = os.path.join(root, fname)
            try:
                with open(path, encoding='utf-8', errors='ignore') as f:
                    for i, line in enumerate(f, 1):
                        if flag_regex.search(line):
                            results.append(f"{path}:{i}: {line.strip()}")
            except Exception as e:
                print(f"[WARN] Could not read {path}: {e}", file=sys.stderr)

if results:
    print("\nFLAGGED LINES FOUND:")
    for r in results:
        print(r)
    print(f"\nTotal flagged lines: {len(results)}")
    sys.exit(1)
else:
    print("No flagged lines found.")
    sys.exit(0)
