{"timestamp": "2025-07-28T11:48:21.057503", "mcp_hub_url": "http://localhost:3000", "summary": {"total_tools": 32, "passed": 0, "failed": 32, "success_rate": 0.0}, "passed_tools": [], "failed_tools": [{"tool": "weather", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "llm_response", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "llm_chat", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "llm_tools", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "doc_analyzer", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "document_tools", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "file_tools", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "file_backup", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "email_sender", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "sms_sender", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "communication_tools", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "calendar_event", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "camera_tools", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "youtube_meta", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "image_gen", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "screenshot", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "voice_embedder", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "ai_toolkit", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "sensor_bridge", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "note_taker", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "phone_mcp", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "face_tracker", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "task_router", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "mobile_control", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "playwright_automation", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "chrome_automation", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "chart_generator", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "badge_awarder", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "hunyuan_video", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "multimodal_ai", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "multilingual_agent", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"tool": "voice_command_processor", "error": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}], "all_results": [{"timestamp": "2025-07-28T11:48:20.845269", "type": "info", "message": "🚀 Starting Comprehensive MCP Tools Test Suite"}, {"timestamp": "2025-07-28T11:48:20.846270", "type": "info", "message": "MCP Hub URL: http://localhost:3000"}, {"timestamp": "2025-07-28T11:48:20.846270", "type": "info", "message": "Total Tools to Test: 32"}, {"timestamp": "2025-07-28T11:48:20.878112", "type": "success", "message": "✅ MCP Hub is running and accessible"}, {"timestamp": "2025-07-28T11:48:20.883627", "type": "error", "message": "❌ Failed to get tool list: 404 Client Error: NOT FOUND for url: http://localhost:3000/api/tools"}, {"timestamp": "2025-07-28T11:48:20.884628", "type": "info", "message": "🚀 Testing all MCP tools in parallel (max 3 workers)..."}, {"timestamp": "2025-07-28T11:48:20.886795", "type": "test", "message": "🧪 Testing weather: Weather information service"}, {"timestamp": "2025-07-28T11:48:20.886795", "type": "test", "message": "🧪 Testing llm_response: LLM chat and responses"}, {"timestamp": "2025-07-28T11:48:20.887839", "type": "test", "message": "🧪 Testing llm_chat: LLM chat interface"}, {"timestamp": "2025-07-28T11:48:20.899262", "type": "error", "message": "❌ weather failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.899262", "type": "test", "message": "🧪 Testing llm_tools: Extended LLM tools"}, {"timestamp": "2025-07-28T11:48:20.902518", "type": "error", "message": "❌ llm_response failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.902518", "type": "error", "message": "❌ llm_chat failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.902518", "type": "test", "message": "🧪 Testing document_tools: Document creation and processing"}, {"timestamp": "2025-07-28T11:48:20.902518", "type": "test", "message": "🧪 Testing doc_analyzer: Document analysis and insights"}, {"timestamp": "2025-07-28T11:48:20.906739", "type": "error", "message": "❌ llm_tools failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.906739", "type": "test", "message": "🧪 Testing file_tools: File operations and management"}, {"timestamp": "2025-07-28T11:48:20.911095", "type": "error", "message": "❌ doc_analyzer failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.911602", "type": "test", "message": "🧪 Testing file_backup: File backup operations"}, {"timestamp": "2025-07-28T11:48:20.911602", "type": "error", "message": "❌ document_tools failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.911602", "type": "test", "message": "🧪 Testing email_sender: Email sending service"}, {"timestamp": "2025-07-28T11:48:20.913610", "type": "error", "message": "❌ file_tools failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.913610", "type": "test", "message": "🧪 Testing sms_sender: SMS and messaging service"}, {"timestamp": "2025-07-28T11:48:20.917803", "type": "error", "message": "❌ file_backup failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.917803", "type": "test", "message": "🧪 Testing communication_tools: General communication tools"}, {"timestamp": "2025-07-28T11:48:20.919649", "type": "error", "message": "❌ email_sender failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.919649", "type": "test", "message": "🧪 Testing calendar_event: Calendar event management"}, {"timestamp": "2025-07-28T11:48:20.920654", "type": "error", "message": "❌ sms_sender failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.920654", "type": "test", "message": "🧪 Testing image_gen: AI image generation"}, {"timestamp": "2025-07-28T11:48:20.924479", "type": "error", "message": "❌ communication_tools failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.924479", "type": "test", "message": "🧪 Testing camera_tools: Camera and photo operations"}, {"timestamp": "2025-07-28T11:48:20.927820", "type": "error", "message": "❌ calendar_event failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.927820", "type": "test", "message": "🧪 Testing screenshot: Screen capture functionality"}, {"timestamp": "2025-07-28T11:48:20.932547", "type": "error", "message": "❌ camera_tools failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.932547", "type": "test", "message": "🧪 Testing youtube_meta: YouTube metadata extraction"}, {"timestamp": "2025-07-28T11:48:20.990419", "type": "error", "message": "❌ youtube_meta failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.990419", "type": "error", "message": "❌ image_gen failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.990419", "type": "error", "message": "❌ screenshot failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.990942", "type": "test", "message": "🧪 Testing voice_embedder: Voice processing and embedding"}, {"timestamp": "2025-07-28T11:48:20.990942", "type": "test", "message": "🧪 Testing note_taker: Voice-to-text transcription"}, {"timestamp": "2025-07-28T11:48:20.990942", "type": "test", "message": "🧪 Testing ai_toolkit: AI analysis toolkit"}, {"timestamp": "2025-07-28T11:48:20.998852", "type": "error", "message": "❌ voice_embedder failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.998852", "type": "test", "message": "🧪 Testing face_tracker: Face detection and tracking"}, {"timestamp": "2025-07-28T11:48:20.998852", "type": "error", "message": "❌ ai_toolkit failed with status 404"}, {"timestamp": "2025-07-28T11:48:20.999970", "type": "test", "message": "🧪 Testing sensor_bridge: IoT sensor data bridge"}, {"timestamp": "2025-07-28T11:48:21.005527", "type": "error", "message": "❌ sensor_bridge failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.006541", "type": "test", "message": "🧪 Testing task_router: Task routing and orchestration"}, {"timestamp": "2025-07-28T11:48:21.009191", "type": "error", "message": "❌ note_taker failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.009191", "type": "test", "message": "🧪 Testing phone_mcp: Phone control via ADB"}, {"timestamp": "2025-07-28T11:48:21.015493", "type": "error", "message": "❌ phone_mcp failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.015493", "type": "test", "message": "🧪 Testing mobile_control: Mobile device automation"}, {"timestamp": "2025-07-28T11:48:21.026039", "type": "error", "message": "❌ face_tracker failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.026039", "type": "error", "message": "❌ task_router failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.026039", "type": "test", "message": "🧪 Testing chrome_automation: Chrome browser automation"}, {"timestamp": "2025-07-28T11:48:21.026039", "type": "test", "message": "🧪 Testing playwright_automation: Playwright web automation"}, {"timestamp": "2025-07-28T11:48:21.031249", "type": "error", "message": "❌ mobile_control failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.032166", "type": "test", "message": "🧪 Testing chart_generator: Chart and graph generation"}, {"timestamp": "2025-07-28T11:48:21.033837", "type": "error", "message": "❌ playwright_automation failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.033837", "type": "test", "message": "🧪 Testing badge_awarder: Badge and achievement system"}, {"timestamp": "2025-07-28T11:48:21.036451", "type": "error", "message": "❌ chrome_automation failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.036451", "type": "test", "message": "🧪 Testing hunyuan_video: HunyuanVideo-13B text-to-video generation"}, {"timestamp": "2025-07-28T11:48:21.038774", "type": "error", "message": "❌ chart_generator failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.038774", "type": "test", "message": "🧪 Testing multimodal_ai: 2025 multimodal AI pipeline (HunyuanVideo + HiDream + Gemini)"}, {"timestamp": "2025-07-28T11:48:21.041045", "type": "error", "message": "❌ badge_awarder failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.041045", "type": "test", "message": "🧪 Testing multilingual_agent: Agent Lee multilingual communication (95+ languages)"}, {"timestamp": "2025-07-28T11:48:21.045921", "type": "error", "message": "❌ hunyuan_video failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.045921", "type": "test", "message": "🧪 Testing voice_command_processor: Voice command processing and TTS in multiple languages"}, {"timestamp": "2025-07-28T11:48:21.045921", "type": "error", "message": "❌ multimodal_ai failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.046934", "type": "error", "message": "❌ multilingual_agent failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.052028", "type": "error", "message": "❌ voice_command_processor failed with status 404"}, {"timestamp": "2025-07-28T11:48:21.052028", "type": "info", "message": "\n============================================================"}, {"timestamp": "2025-07-28T11:48:21.052028", "type": "info", "message": "📊 MCP TOOLS TEST REPORT"}, {"timestamp": "2025-07-28T11:48:21.052028", "type": "info", "message": "============================================================"}, {"timestamp": "2025-07-28T11:48:21.053029", "type": "info", "message": "Total Tools Tested: 32"}, {"timestamp": "2025-07-28T11:48:21.053029", "type": "success", "message": "✅ Passed: 0"}, {"timestamp": "2025-07-28T11:48:21.053029", "type": "error", "message": "❌ Failed: 32"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "info", "message": "📈 Success Rate: 0.0%"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "\n❌ FAILED TOOLS (32):"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • weather: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • llm_response: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • llm_chat: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • llm_tools: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • doc_analyzer: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • document_tools: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • file_tools: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.053612", "type": "error", "message": "  • file_backup: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • email_sender: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • sms_sender: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • communication_tools: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • calendar_event: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • camera_tools: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • youtube_meta: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • image_gen: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • screenshot: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • voice_embedder: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • ai_toolkit: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • sensor_bridge: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • note_taker: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.054930", "type": "error", "message": "  • phone_mcp: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • face_tracker: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • task_router: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • mobile_control: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • playwright_automation: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • chrome_automation: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • chart_generator: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • badge_awarder: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • hunyuan_video: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.056439", "type": "error", "message": "  • multimodal_ai: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.057503", "type": "error", "message": "  • multilingual_agent: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}, {"timestamp": "2025-07-28T11:48:21.057503", "type": "error", "message": "  • voice_command_processor: HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n"}]}