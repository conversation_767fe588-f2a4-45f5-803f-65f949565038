version: '3.8'

services:
  xtts:
    build:
      context: ./xtts
      dockerfile: Dockerfile
    container_name: xtts_server
    ports:
      - "5002:5002"
    restart: unless-stopped

  webhook:
    build:
      context: ./xtts
      dockerfile: Dockerfile
    container_name: xtts_webhook
    command: python webhook_server.py
    ports:
      - "8080:8080"
    environment:
      XTTS_API: http://xtts:5002/api/tts
    depends_on:
      - xtts
    restart: unless-stopped
