// Centralized configuration for backend URL
const backendUrl = (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1'))
  ? 'http://localhost:8000'
  : 'https://agentlee.fly.dev';

/**
 * Fetches a LiveKit token from the backend.
 * @param identity The identity of the user for the LiveKit room.
 * @param room The name of the room to join.
 * @returns A promise that resolves with the token and server URL.
 */
export const getLiveKitToken = async (identity: string = 'user', room: string = 'agent-lee-room') => {
  try {
    const response = await fetch(`${backendUrl}/api/livekit/token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ identity, room })
    });

    if (!response.ok) {
      throw new Error(`Failed to get LiveKit token: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error('LiveKit Token Error:', error);
    // Return details for a local dev server as a fallback
    return { token: null, server_url: 'ws://localhost:7880' };
  }
};

/**
 * Sends a message to the Agent <PERSON> backend chat endpoint.
 * @param message The text message to send.
 * @param userId The ID of the user sending the message.
 * @returns A promise that resolves with the agent's response text.
 */
export const sendMessageToAgent = async (message: string, userId: string = 'user'): Promise<string> => {
  try {
    const response = await fetch(`${backendUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: message,
        user_id: userId,
        conversation_type: 'voice' // Or could be dynamic
      })
    });

    if (!response.ok) {
      throw new Error(`Backend chat API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.response || "I received that, but I'm not sure how to respond.";
  } catch (error) {
    console.error('Send Message Error:', error);
    return "I'm having trouble connecting to my backend. Please try again.";
  }
};