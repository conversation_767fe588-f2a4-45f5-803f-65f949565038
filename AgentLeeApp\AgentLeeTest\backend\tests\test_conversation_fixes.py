#!/usr/bin/env python3
"""
Test script to verify conversation fixes and frontend integration
"""

import requests
import json
import time
from datetime import datetime

# Test configuration
BACKEND_URL = "http://localhost:8000"
TEST_USER = "test_conversation_user"

def test_conversation_state():
    """Test conversation state management"""
    print("🧪 Testing Conversation State Management")
    print("=" * 50)
    
    from conversation_state import get_conversation_state
    
    conv_state = get_conversation_state()
    
    # Test first introduction
    should_introduce_first = conv_state.should_introduce(TEST_USER)
    print(f"✓ Should introduce on first contact: {should_introduce_first}")
    
    # Mark as introduced
    conv_state.mark_introduced(TEST_USER)
    
    # Test second contact
    should_introduce_second = conv_state.should_introduce(TEST_USER)
    print(f"✓ Should NOT introduce on second contact: {not should_introduce_second}")
    
    # Test context addition
    conv_state.add_context(TEST_USER, "Hello Agent Lee", "Hello! How can I help you?")
    context = conv_state.get_context_summary(TEST_USER)
    print(f"✓ Context tracking works: {bool(context)}")
    
    return True

def test_chat_endpoint():
    """Test the new chat endpoint"""
    print("\n🌐 Testing Chat Endpoint")
    print("=" * 30)
    
    # Test first message (should introduce)
    response1 = requests.post(f"{BACKEND_URL}/api/chat", json={
        "message": "Hello",
        "user_id": "new_test_user",
        "context": "voice_chat"
    })
    
    if response1.status_code == 200:
        data1 = response1.json()
        print(f"✓ First message response: {data1['response'][:50]}...")
        has_introduction = "Agent Lee" in data1['response']
        print(f"✓ Contains introduction: {has_introduction}")
    else:
        print(f"❌ Chat endpoint error: {response1.status_code}")
        return False
    
    # Test second message (should NOT introduce)
    time.sleep(1)
    response2 = requests.post(f"{BACKEND_URL}/api/chat", json={
        "message": "What can you do?",
        "user_id": "new_test_user",
        "context": "voice_chat"
    })
    
    if response2.status_code == 200:
        data2 = response2.json()
        print(f"✓ Second message response: {data2['response'][:50]}...")
        no_repeat_intro = "I'm Agent Lee" not in data2['response']
        print(f"✓ No repeated introduction: {no_repeat_intro}")
    else:
        print(f"❌ Second chat request error: {response2.status_code}")
        return False
    
    return True

def test_contextual_responses():
    """Test different types of contextual responses"""
    print("\n💬 Testing Contextual Responses")
    print("=" * 35)
    
    test_messages = [
        ("What's the weather like?", "weather"),
        ("Take a screenshot", "screenshot"),
        ("Hello there", "greeting"),
        ("What can you help me with?", "help"),
        ("How are you doing today?", "general")
    ]
    
    for message, expected_type in test_messages:
        response = requests.post(f"{BACKEND_URL}/api/chat", json={
            "message": message,
            "user_id": f"context_test_user_{expected_type}",
            "context": "test"
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ {expected_type.title()}: {data['response'][:60]}...")
        else:
            print(f"❌ {expected_type.title()} test failed: {response.status_code}")
    
    return True

def test_frontend_integration():
    """Test frontend integration points"""
    print("\n🎨 Testing Frontend Integration")
    print("=" * 35)
    
    # Test login endpoint
    login_response = requests.post(f"{BACKEND_URL}/api/login", json={
        "name": "test_user",
        "email": "<EMAIL>"
    })
    
    if login_response.status_code == 200:
        login_data = login_response.json()
        print("✓ Login endpoint works")
        token = login_data.get('token')
        
        if token:
            # Test LiveKit token generation
            livekit_response = requests.post(f"{BACKEND_URL}/api/livekit/token", 
                headers={"Authorization": f"Bearer {token}"},
                json={
                    "identity": "test_user",
                    "room": "agent-lee-room",
                    "metadata": {"user_id": "test_user"}
                }
            )
            
            if livekit_response.status_code == 200:
                livekit_data = livekit_response.json()
                print("✓ LiveKit token generation works")
                print(f"✓ Metadata passed: {livekit_data.get('metadata', {})}")
            else:
                print(f"❌ LiveKit token error: {livekit_response.status_code}")
        
    else:
        print(f"❌ Login endpoint error: {login_response.status_code}")
    
    return True

def main():
    """Run all tests"""
    print("🔧 Agent Lee Conversation Fixes Test Suite")
    print("=" * 55)
    print(f"Test started at: {datetime.now()}")
    print(f"Backend URL: {BACKEND_URL}")
    print()
    
    tests = [
        ("Conversation State", test_conversation_state),
        ("Chat Endpoint", test_chat_endpoint),
        ("Contextual Responses", test_contextual_responses),
        ("Frontend Integration", test_frontend_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 55)
    print("📊 TEST RESULTS")
    print("=" * 55)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All conversation fixes working correctly!")
        print("\n✅ FIXES VERIFIED:")
        print("  • Agent Lee only introduces himself once")
        print("  • Conversation state is tracked properly")
        print("  • Contextual responses work")
        print("  • Frontend integration improved")
        print("  • No repeated introductions")
        return 0
    else:
        print("⚠️ Some issues detected. Check logs above.")
        return 1

if __name__ == "__main__":
    exit(main())