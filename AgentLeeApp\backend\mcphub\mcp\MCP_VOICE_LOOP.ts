
// ...existing code...
import { MCPServer } from 'model-context-protocol';

export async function startServer(port = 3005) {
  const server = new MCPServer({ port });

  server.registerTool('voiceLoop', {
    title: 'Voice Loop',
    description: 'Echo back audio repeatedly for testing',
    parameters: {
      type: 'object',
      properties: {
        audio_base64: { type: 'string' },
        loops: { type: 'integer', default: 1 }
      },
      required: ['audio_base64']
    },
    returns: {
      type: 'object',
      properties: {
        result_audio_base64: { type: 'string' }
      }
    }
  }, async ({ audio_base64, loops }) => {
    let data = audio_base64;
    for (let i = 1; i < loops; i++) {
      data += audio_base64;
    }
    return { result_audio_base64: data };
  });

  await server.listen();
  console.log(`VoiceLoop MCP server running on port ${port}`);
}
