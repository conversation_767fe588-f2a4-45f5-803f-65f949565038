import { useState, useCallback } from 'react';
import { ChatMessage } from '../types';
import { sendMessageToAgent } from '../services/agentLeeService';

interface UseAgentLeeChatReturn {
  messages: ChatMessage[];
  isSpeaking: boolean;
  isLoading: boolean;
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
}

export const useAgentLeeChat = (): UseAgentLeeChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'user',
      text: content,
      content: content,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Call backend API for agent response
      const agentResponse = await sendMessageToAgent(content);
      const agentMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        sender: 'agent',
        text: agentResponse,
        content: agentResponse,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, agentMessage]);

      // Text-to-speech for agent response
      if ('speechSynthesis' in window) {
        setIsSpeaking(true);
        const utterance = new SpeechSynthesisUtterance(agentMessage.text);
        utterance.onend = () => setIsSpeaking(false);
        speechSynthesis.speak(utterance);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        sender: 'agent',
        text: 'Sorry, I encountered an error processing your request. Please try again.',
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  return {
    messages,
    isSpeaking,
    isLoading,
    sendMessage,
    clearMessages
  };
};
