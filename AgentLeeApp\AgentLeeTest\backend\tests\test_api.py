#!/usr/bin/env python3
"""
AgentLee Backend API Tests
Comprehensive testing of all API endpoints and functionality
"""

import requests
import json
import time
import sys
from datetime import datetime

class APITester:
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.api_token = "changeme"
        self.results = []
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        if passed:
            print(f"✅ {test_name}")
        else:
            print(f"❌ {test_name}")
            if details:
                print(f"   Details: {details}")

    def test_health_endpoint(self):
        """Test health check endpoint"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                self.log_test("Health Check Endpoint", True)
            else:
                self.log_test("Health Check Endpoint", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Health Check Endpoint", False, str(e))

    def test_root_endpoint(self):
        """Test root endpoint"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                self.log_test("Root Endpoint", True)
            else:
                self.log_test("Root Endpoint", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Root Endpoint", False, str(e))

    def test_api_authentication(self):
        """Test API authentication"""
        # Test with valid token
        try:
            response = requests.post(
                f"{self.base_url}/api/task/parse",
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.api_token}'
                },
                json={'speaker': 'test_user', 'text': 'Hello'},
                timeout=10
            )
            if response.status_code == 200:
                self.log_test("API Authentication (Valid Token)", True)
            else:
                self.log_test("API Authentication (Valid Token)", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("API Authentication (Valid Token)", False, str(e))

        # Test with invalid token
        try:
            response = requests.post(
                f"{self.base_url}/api/task/parse",
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer invalid_token'
                },
                json={'speaker': 'test_user', 'text': 'Hello'},
                timeout=10
            )
            if response.status_code == 401:
                self.log_test("API Authentication (Invalid Token)", True)
            else:
                self.log_test("API Authentication (Invalid Token)", False, f"Expected 401, got {response.status_code}")
        except Exception as e:
            self.log_test("API Authentication (Invalid Token)", False, str(e))

    def test_task_parsing(self):
        """Test task parsing endpoint"""
        test_cases = [
            {'speaker': 'user1', 'text': 'Hello Agent Lee'},
            {'speaker': 'user2', 'text': 'What can you do?'},
            {'speaker': 'user3', 'text': 'Help me with a task'},
            {'speaker': 'user4', 'text': 'Generate a report'},
        ]
        
        for i, test_case in enumerate(test_cases):
            try:
                response = requests.post(
                    f"{self.base_url}/api/task/parse",
                    headers={
                        'Content-Type': 'application/json',
                        'Authorization': f'Bearer {self.api_token}'
                    },
                    json=test_case,
                    timeout=10
                )
                if response.status_code == 200:
                    self.log_test(f"Task Parsing Test {i+1}", True)
                else:
                    self.log_test(f"Task Parsing Test {i+1}", False, f"Status: {response.status_code}")
            except Exception as e:
                self.log_test(f"Task Parsing Test {i+1}", False, str(e))

    def test_memory_operations(self):
        """Test memory search and operations"""
        # Test memory search
        try:
            response = requests.get(
                f"{self.base_url}/api/memory/search?query=test",
                headers={'Authorization': f'Bearer {self.api_token}'},
                timeout=5
            )
            if response.status_code in [200, 404]:
                self.log_test("Memory Search", True)
            else:
                self.log_test("Memory Search", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Memory Search", False, str(e))

        # Test memory search with speaker filter
        try:
            response = requests.get(
                f"{self.base_url}/api/memory/search?query=test&speaker=user1",
                headers={'Authorization': f'Bearer {self.api_token}'},
                timeout=5
            )
            if response.status_code in [200, 404]:
                self.log_test("Memory Search with Speaker Filter", True)
            else:
                self.log_test("Memory Search with Speaker Filter", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Memory Search with Speaker Filter", False, str(e))

    def test_session_management(self):
        """Test session management endpoints"""
        # Test get sessions
        try:
            response = requests.get(
                f"{self.base_url}/api/sessions",
                headers={'Authorization': f'Bearer {self.api_token}'},
                timeout=5
            )
            if response.status_code in [200, 404]:
                self.log_test("Get Sessions", True)
            else:
                self.log_test("Get Sessions", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Get Sessions", False, str(e))

    def test_speaker_statistics(self):
        """Test speaker statistics endpoints"""
        # Test speaker stats
        try:
            response = requests.get(
                f"{self.base_url}/api/speakers/test_user/stats",
                headers={'Authorization': f'Bearer {self.api_token}'},
                timeout=5
            )
            if response.status_code in [200, 404]:
                self.log_test("Speaker Statistics", True)
            else:
                self.log_test("Speaker Statistics", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Speaker Statistics", False, str(e))

    def test_tools_endpoint(self):
        """Test tools listing endpoint"""
        try:
            response = requests.get(
                f"{self.base_url}/api/tools",
                headers={'Authorization': f'Bearer {self.api_token}'},
                timeout=5
            )
            if response.status_code in [200, 404]:
                self.log_test("Tools Listing", True)
            else:
                self.log_test("Tools Listing", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Tools Listing", False, str(e))

    def test_error_handling(self):
        """Test error handling"""
        # Test invalid JSON
        try:
            response = requests.post(
                f"{self.base_url}/api/task/parse",
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.api_token}'
                },
                data="invalid json",
                timeout=5
            )
            if response.status_code == 400:
                self.log_test("Invalid JSON Handling", True)
            else:
                self.log_test("Invalid JSON Handling", False, f"Expected 400, got {response.status_code}")
        except Exception as e:
            self.log_test("Invalid JSON Handling", False, str(e))

        # Test missing required fields
        try:
            response = requests.post(
                f"{self.base_url}/api/task/parse",
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.api_token}'
                },
                json={},
                timeout=5
            )
            if response.status_code == 400:
                self.log_test("Missing Fields Handling", True)
            else:
                self.log_test("Missing Fields Handling", False, f"Expected 400, got {response.status_code}")
        except Exception as e:
            self.log_test("Missing Fields Handling", False, str(e))

    def test_performance(self):
        """Test API performance"""
        # Test response time
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=5)
            response_time = time.time() - start_time
            
            if response_time < 2.0:
                self.log_test("Response Time", True, f"{response_time:.2f}s")
            else:
                self.log_test("Response Time", False, f"Too slow: {response_time:.2f}s")
        except Exception as e:
            self.log_test("Response Time", False, str(e))

    def run_all_tests(self):
        """Run all API tests"""
        print("🔧 AgentLee Backend API Tests")
        print("=" * 40)
        
        self.test_health_endpoint()
        self.test_root_endpoint()
        self.test_api_authentication()
        self.test_task_parsing()
        self.test_memory_operations()
        self.test_session_management()
        self.test_speaker_statistics()
        self.test_tools_endpoint()
        self.test_error_handling()
        self.test_performance()
        
        self.generate_summary()

    def generate_summary(self):
        """Generate test summary"""
        passed = sum(1 for r in self.results if r['passed'])
        total = len(self.results)
        percentage = (passed / total * 100) if total > 0 else 0
        
        print("\n" + "=" * 40)
        print("📊 API TEST SUMMARY")
        print("=" * 40)
        print(f"Passed: {passed}/{total} ({percentage:.1f}%)")
        
        if passed == total:
            print("🎉 ALL API TESTS PASSED!")
        elif passed > 0:
            print("⚠️  PARTIAL SUCCESS - Some tests failed")
        else:
            print("❌ ALL API TESTS FAILED!")

def main():
    """Main test runner"""
    tester = APITester()
    tester.run_all_tests()

if __name__ == "__main__":
    main() 