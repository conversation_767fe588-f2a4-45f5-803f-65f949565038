
from flask import Flask, request, jsonify
from flask_cors import CORS
from memory_db import save_to_memory, search_memory, save_session, load_sessions, get_speaker_stats
from dispatcher import dispatch, run_tool
import os
import threading
import time
import logging
from functools import wraps

# --- Logging setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')

# --- Simple token-based authentication ---
API_TOKEN = os.environ.get('AGENT_LEE_API_TOKEN', 'changeme')
def auth_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token or token.replace('Bearer ', '') != API_TOKEN:
            logging.warning('Unauthorized access attempt.')
            return jsonify({'error': 'Unauthorized'}), 401
        return f(*args, **kwargs)
    return decorated

app = Flask(__name__)

# --- Add root and favicon routes for Playwright/browser compatibility ---
from flask import send_from_directory
import os

@app.route("/")
def root():
    return "Agent Lee backend is running."

@app.route("/favicon.ico")
def favicon():
    return send_from_directory(
        os.path.join(app.root_path, '../frontend/clean_clone'),
        'favicon.ico',
        mimetype='image/vnd.microsoft.icon'
    )
CORS(app)  # Enable CORS for frontend communication


@app.route('/api/task/parse', methods=['POST'])
@auth_required
def handle_task():
    """Enhanced task handler with multi-speaker support and input validation"""
    try:
        data = request.get_json(force=True, silent=True)
        if not data:
            logging.error('No JSON data provided')
            return jsonify({"error": "No JSON data provided"}), 400

        speaker = data.get('speaker', 'unknown')
        text = data.get('text', '')
        tool = data.get('tool')

        # Input validation
        if not isinstance(speaker, str) or not speaker.strip():
            return jsonify({"error": "Invalid or missing speaker"}), 400
        if not isinstance(text, str) or not text.strip():
            return jsonify({"error": "No text provided"}), 400
        if tool and not isinstance(tool, str):
            return jsonify({"error": "Invalid tool name"}), 400

        # Save to memory and session history
        save_to_memory(speaker, text)
        save_session(speaker, text)

        # Dispatch to appropriate tool
        if tool:
            task = {"tool": tool, "input": text}
            result = dispatch(task, speaker)
        else:
            # Use legacy run_tool for backward compatibility
            result = run_tool(text, speaker)

        return result if hasattr(result, 'status_code') else jsonify({'response': result})

    except Exception as e:
        logging.exception('Server error in handle_task')
        return jsonify({"error": f"Server error: {str(e)}"}), 500


@app.route('/api/memory/search', methods=['GET'])
@auth_required
def memory_lookup():
    """Enhanced memory search with better filtering and validation"""
    try:
        speaker = request.args.get('speaker')
        query = request.args.get('query')
        if speaker and not isinstance(speaker, str):
            return jsonify({"error": "Invalid speaker"}), 400
        if query and not isinstance(query, str):
            return jsonify({"error": "Invalid query"}), 400
        results = search_memory(speaker, query)
        return jsonify({'results': results})
    except Exception as e:
        logging.exception('Memory search error')
        return jsonify({"error": f"Memory search error: {str(e)}"}), 500


@app.route('/api/sessions', methods=['GET'])
@auth_required
def get_sessions():
    """Get all speaker sessions"""
    try:
        sessions = load_sessions()
        return jsonify(sessions)
    except Exception as e:
        logging.exception('Session retrieval error')
        return jsonify({"error": f"Session retrieval error: {str(e)}"}), 500


@app.route('/api/speakers/<speaker>/stats', methods=['GET'])
@auth_required
def speaker_stats(speaker):
    """Get statistics for a specific speaker (with validation)"""
    try:
        if not isinstance(speaker, str) or not speaker.strip():
            return jsonify({"error": "Invalid speaker"}), 400
        stats = get_speaker_stats(speaker)
        return jsonify(stats)
    except Exception as e:
        logging.exception('Stats error')
        return jsonify({"error": f"Stats error: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({"status": "OK", "service": "Agent Lee Backend"})

@app.route('/api/tools', methods=['GET'])
@auth_required
def list_tools():
    """List available tools"""
    try:
        from dispatcher import TOOL_REGISTRY
        return jsonify({"tools": TOOL_REGISTRY})
    except Exception as e:
        return jsonify({"error": f"Tool listing error: {str(e)}"}), 500

@app.route('/api/speakers/<speaker>/badges', methods=['GET'])
def get_speaker_badges(speaker):
    """Get badges for a specific speaker"""
    try:
        from .mcp.badge_awarder import get_speaker_badges
        badges = get_speaker_badges(speaker)
        return jsonify(badges)
    except Exception as e:
        return jsonify({"error": f"Badge retrieval error: {str(e)}"}), 500

@app.route('/api/leaderboard', methods=['GET'])
def get_leaderboard():
    """Get badge leaderboard"""
    try:
        from .mcp.badge_awarder import get_leaderboard
        limit = int(request.args.get('limit', 10))
        leaderboard = get_leaderboard(limit)
        return jsonify({"leaderboard": leaderboard})
    except Exception as e:
        return jsonify({"error": f"Leaderboard error: {str(e)}"}), 500

@app.route('/api/speakers/<speaker>/resume', methods=['POST'])
def generate_resume(speaker):
    """Generate resume for a speaker"""
    try:
        data = request.json or {}
        resume_type = data.get('type', 'standard')
        additional_info = data.get('additional_info', '')

        from .mcp.resume_builder import run as build_resume
        task = {
            "input": additional_info,
            "type": resume_type
        }
        result = build_resume(task, speaker)
        return result
    except Exception as e:
        return jsonify({"error": f"Resume generation error: {str(e)}"}), 500

@app.route('/api/speakers/<speaker>/certificate', methods=['POST'])
def generate_certificate(speaker):
    """Generate certificate for a speaker"""
    try:
        data = request.json or {}
        achievement = data.get('achievement', 'Completion')
        description = data.get('description', '')

        from .mcp.pdf_creator import create_certificate
        result = create_certificate(speaker, achievement, description)
        return result
    except Exception as e:
        return jsonify({"error": f"Certificate generation error: {str(e)}"}), 500

# Optional: Real-time voice processing (requires speech_recognition)
def background_voice_loop():
    """Background voice processing loop"""
    try:
        import speech_recognition as sr
        recognizer = sr.Recognizer()
        mic = sr.Microphone()

        print("[VOICE] Voice loop started. Listening for commands...")

        with mic as source:
            recognizer.adjust_for_ambient_noise(source)

        while True:
            try:
                with mic as source:
                    # Listen for audio with timeout
                    audio = recognizer.listen(source, timeout=1, phrase_time_limit=5)

                # Recognize speech
                command = recognizer.recognize_google(audio)
                speaker = "VoiceUser"  # Could be enhanced with speaker identification

                print(f"[VOICE] {speaker}: {command}")

                # Save and process command
                save_to_memory(speaker, command)
                save_session(speaker, command)

                # Dispatch to LLM chat for processing
                task = {"tool": "llm_chat", "input": command}
                result = dispatch(task, speaker)

                print(f"[BOT] Response: {result}")

            except sr.WaitTimeoutError:
                # No speech detected, continue listening
                pass
            except sr.UnknownValueError:
                # Speech not understood
                print("[VOICE] Could not understand audio")
            except Exception as e:
                print(f"[VOICE] Voice loop error: {e}")
                time.sleep(1)

    except ImportError:
        print("[VOICE] Speech recognition not available. Install speech_recognition and pyaudio.")
    except Exception as e:
        print(f"[VOICE] Voice loop initialization error: {e}")

# Start voice loop in background (optional)
voice_loop_type = os.getenv('VOICE_LOOP_TYPE', 'none').lower()

if voice_loop_type == 'google':
    try:
        from voice_loop_google import start_voice_loop
        start_voice_loop()
        print("[VOICE] Google Voice Loop with diarization enabled")
    except ImportError:
        print("[ERROR] Google Voice Loop not available. Install google-cloud-speech")
elif voice_loop_type == 'basic':
    voice_thread = threading.Thread(target=background_voice_loop, daemon=True)
    voice_thread.start()
    print("[VOICE] Basic voice processing enabled")
else:
    print("[VOICE] Voice processing disabled. Set VOICE_LOOP_TYPE=google or basic to enable")

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    app.run(host='0.0.0.0', port=port, debug=debug)
