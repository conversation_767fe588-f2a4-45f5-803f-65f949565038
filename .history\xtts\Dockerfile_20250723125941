
# Base Python image
FROM python:3.10-slim

# Install ffmpeg for audio processing
RUN apt-get update && apt-get install -y ffmpeg && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Pre-download XTTS model inside the image
RUN python -c "from TTS.api import TTS; TTS(model_name='tts_models/multilingual/multi-dataset/xtts_v2')"

# Ensure temp folder exists
RUN mkdir -p /tmp

# Copy project files
COPY . .

# Expose port for Gunicorn
EXPOSE 5002

# Start the TTS server using Gunicorn for production
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5002", "server:app"]
