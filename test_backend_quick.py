#!/usr/bin/env python3
"""
Quick Backend API Tests for <PERSON> Lee
"""

import requests
import json
import time
from datetime import datetime

class APITester:
    def __init__(self):
        self.base_url = 'http://localhost:8000'
        self.api_token = 'changeme'
        self.results = []
        
    def log_test(self, test_name, passed, details=''):
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        if passed:
            print(f'✅ {test_name}')
        else:
            print(f'❌ {test_name}')
            if details:
                print(f'   Details: {details}')

    def test_health_endpoint(self):
        try:
            response = requests.get(f'{self.base_url}/health', timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'OK':
                    self.log_test('Health Check Endpoint', True)
                else:
                    self.log_test('Health Check Endpoint', False, f'Unexpected response: {data}')
            else:
                self.log_test('Health Check Endpoint', False, f'Status: {response.status_code}')
        except Exception as e:
            self.log_test('Health Check Endpoint', False, str(e))

    def test_root_endpoint(self):
        try:
            response = requests.get(f'{self.base_url}/', timeout=5)
            if response.status_code == 200:
                if 'Agent Lee backend is running' in response.text:
                    self.log_test('Root Endpoint', True)
                else:
                    self.log_test('Root Endpoint', False, f'Unexpected response: {response.text}')
            else:
                self.log_test('Root Endpoint', False, f'Status: {response.status_code}')
        except Exception as e:
            self.log_test('Root Endpoint', False, str(e))

    def test_tools_endpoint(self):
        try:
            response = requests.get(
                f'{self.base_url}/api/tools',
                headers={'Authorization': f'Bearer {self.api_token}'},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                if 'tools' in data and len(data['tools']) > 0:
                    self.log_test('Tools Listing', True, f'Found {len(data["tools"])} tools')
                else:
                    self.log_test('Tools Listing', False, 'No tools found in response')
            else:
                self.log_test('Tools Listing', False, f'Status: {response.status_code}')
        except Exception as e:
            self.log_test('Tools Listing', False, str(e))

    def test_sessions_endpoint(self):
        try:
            response = requests.get(
                f'{self.base_url}/api/sessions',
                headers={'Authorization': f'Bearer {self.api_token}'},
                timeout=5
            )
            if response.status_code == 200:
                self.log_test('Sessions Endpoint', True)
            else:
                self.log_test('Sessions Endpoint', False, f'Status: {response.status_code}')
        except Exception as e:
            self.log_test('Sessions Endpoint', False, str(e))

    def test_authentication(self):
        try:
            # Test without token
            response = requests.get(f'{self.base_url}/api/tools', timeout=5)
            if response.status_code == 401:
                self.log_test('Authentication (No Token)', True)
            else:
                self.log_test('Authentication (No Token)', False, f'Expected 401, got {response.status_code}')
        except Exception as e:
            self.log_test('Authentication (No Token)', False, str(e))

    def run_tests(self):
        print('🧪 Running Backend API Tests...')
        print('=' * 50)
        
        self.test_health_endpoint()
        self.test_root_endpoint()
        self.test_authentication()
        self.test_tools_endpoint()
        self.test_sessions_endpoint()
        
        print('=' * 50)
        passed = sum(1 for r in self.results if r['passed'])
        total = len(self.results)
        print(f'📊 Results: {passed}/{total} tests passed')
        
        if passed == total:
            print('🎉 All tests passed!')
            return True
        else:
            print('⚠️  Some tests failed')
            return False

if __name__ == '__main__':
    tester = APITester()
    success = tester.run_tests()
    exit(0 if success else 1)
