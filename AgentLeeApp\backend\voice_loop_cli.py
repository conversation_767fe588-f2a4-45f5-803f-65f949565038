#!/usr/bin/env python3
"""
🎤 <PERSON> Lee — CLI Voice Loop with Real-time Streaming & Speaker Memory
Enhanced version with persistent speaker mapping and CLI-ready interface
"""

import os
import io
import json
import time
import threading
import argparse
import signal
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional, Generator

# Import Agent Lee components
try:
    from memory_db import save_to_memory, save_session
    from dispatcher import dispatch
    from auto_task_generator import infer_task_tool_from_text
except ImportError as e:
    print(f"⚠️ Warning: Could not import Agent Lee components: {e}")
    print("Running in standalone mode...")
    
    # Fallback functions for standalone mode
    def save_to_memory(speaker, text):
        print(f"💾 Memory: {speaker} -> {text}")
    
    def save_session(speaker, text):
        print(f"📝 Session: {speaker} -> {text}")
    
    def dispatch(task, speaker):
        print(f"🤖 Dispatch: {task} for {speaker}")
        # Simulate tool execution without Flask context
        tool_name = task.get("tool", "unknown")
        input_text = task.get("input", "")
        
        if tool_name == "phone_mcp":
            return {"status": "success", "message": f"Simulated phone call: {input_text}"}
        elif tool_name == "resume_builder":
            return {"status": "success", "message": f"Simulated resume generation: {input_text}"}
        elif tool_name == "llm_chat":
            return {"status": "success", "message": f"Simulated chat response to: {input_text}"}
        else:
            return {"status": "simulated", "tool": tool_name, "input": input_text}
    
    def infer_task_tool_from_text(text):
        # Simple task inference for standalone mode
        text_lower = text.lower()
        if "call" in text_lower or "phone" in text_lower:
            return {"tool": "phone_mcp", "input": text}
        elif "resume" in text_lower:
            return {"tool": "resume_builder", "input": text}
        elif "weather" in text_lower:
            return {"tool": "weather", "input": text}
        return None

# Configuration
SPEAKER_MAP_FILE = "speaker_map.json"
CONFIG_FILE = "voice_config.json"
RATE = 16000
CHUNK = int(RATE / 10)  # 100ms chunks

class VoiceLoopCLI:
    def __init__(self):
        self.speaker_map: Dict[str, str] = {}
        self.config: Dict = {}
        self.running = False
        self.audio_stream = None
        self.speech_client = None
        self.load_speaker_map()
        self.load_config()
        
    def load_speaker_map(self):
        """Load persistent speaker mapping from JSON file"""
        try:
            if os.path.exists(SPEAKER_MAP_FILE):
                with open(SPEAKER_MAP_FILE, 'r') as f:
                    self.speaker_map = json.load(f)
                print(f"📋 Loaded {len(self.speaker_map)} known speakers")
            else:
                print("📋 No existing speaker map found, will create new one")
        except Exception as e:
            print(f"⚠️ Error loading speaker map: {e}")
            self.speaker_map = {}
    
    def save_speaker_map(self):
        """Save speaker mapping to persistent storage"""
        try:
            with open(SPEAKER_MAP_FILE, 'w') as f:
                json.dump(self.speaker_map, f, indent=2)
            print(f"💾 Saved speaker map with {len(self.speaker_map)} speakers")
        except Exception as e:
            print(f"⚠️ Error saving speaker map: {e}")
    
    def load_config(self):
        """Load voice loop configuration"""
        default_config = {
            "language_code": "en-US",
            "speaker_count": 10,
            "enable_enhanced": True,
            "interim_results": False,
            "model": "video",
            "auto_save_interval": 30
        }
        
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r') as f:
                    self.config = {**default_config, **json.load(f)}
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            print(f"⚠️ Error loading config: {e}")
            self.config = default_config
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(CONFIG_FILE, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"⚠️ Error saving config: {e}")
    
    def prompt_speaker_name(self, speaker_tag: str) -> str:
        """Prompt user for speaker name (only once per speaker)"""
        if speaker_tag in self.speaker_map:
            return self.speaker_map[speaker_tag]
        
        print(f"\n🎯 New speaker detected: {speaker_tag}")
        print("Known speakers:", list(self.speaker_map.values()) if self.speaker_map else "None")
        
        while True:
            name = input(f"Enter name for {speaker_tag} (or press Enter for auto-name): ").strip()
            if not name:
                name = f"Speaker_{speaker_tag}"
                break
            elif name in self.speaker_map.values():
                print(f"⚠️ Name '{name}' already exists. Please choose a different name.")
                continue
            else:
                break
        
        self.speaker_map[speaker_tag] = name
        self.save_speaker_map()
        print(f"✅ Registered {speaker_tag} as '{name}'")
        return name
    
    def get_live_mic_stream(self) -> Generator[bytes, None, None]:
        """Get live microphone audio stream with error handling"""
        try:
            import pyaudio
            
            audio_interface = pyaudio.PyAudio()
            
            # Find best input device
            device_index = None
            for i in range(audio_interface.get_device_count()):
                device_info = audio_interface.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    device_index = i
                    print(f"🎤 Using audio device: {device_info['name']}")
                    break
            
            self.audio_stream = audio_interface.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=RATE,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=CHUNK
            )
            
            print(f"🎤 Audio stream started (Rate: {RATE}Hz, Chunk: {CHUNK})")
            
            while self.running:
                try:
                    data = self.audio_stream.read(CHUNK, exception_on_overflow=False)
                    yield data
                except Exception as e:
                    print(f"🎤 Audio stream error: {e}")
                    break
                    
        except ImportError:
            print("❌ PyAudio not available. Install: pip install pyaudio")
            return
        except Exception as e:
            print(f"❌ Failed to initialize audio stream: {e}")
            return
    
    def initialize_speech_client(self):
        """Initialize Google Speech-to-Text client"""
        try:
            from google.cloud import speech_v1p1beta1 as speech
            
            self.speech_client = speech.SpeechClient()
            print("✅ Google Speech-to-Text client initialized")
            return True
            
        except ImportError:
            print("❌ Google Cloud Speech not available. Install: pip install google-cloud-speech")
            return False
        except Exception as e:
            print(f"❌ Failed to initialize speech client: {e}")
            return False
    
    def handle_phrase(self, speaker: str, text: str, confidence: float = 0.0):
        """Process recognized speech phrase with enhanced logging"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        confidence_str = f"({confidence:.1%})" if confidence > 0 else ""
        
        print(f"[{timestamp}] {speaker} 🎙️ {text} {confidence_str}")
        
        # Save to memory and session
        save_to_memory(speaker, text)
        save_session(speaker, text)
        
        # Infer and execute task
        task = infer_task_tool_from_text(text)
        if task:
            print(f"🤖 Agent Lee executing: {task['tool']}")
            try:
                result = dispatch(task, speaker)
                print(f"✅ Result: {result}")
            except Exception as e:
                print(f"❌ Task execution failed: {e}")
        else:
            # Default to LLM chat if no specific tool detected
            task = {"tool": "llm_chat", "input": text}
            try:
                result = dispatch(task, speaker)
                print(f"💬 Chat response: {result}")
            except Exception as e:
                print(f"❌ Chat failed: {e}")
    
    def run_streaming_recognition(self):
        """Main streaming recognition loop with Google Speech-to-Text"""
        if not self.initialize_speech_client():
            return self.run_fallback_recognition()
        
        try:
            from google.cloud import speech_v1p1beta1 as speech
            
            mic_stream = self.get_live_mic_stream()
            if not mic_stream:
                return
            
            config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=RATE,
                language_code=self.config["language_code"],
                enable_speaker_diarization=True,
                diarization_speaker_count=self.config["speaker_count"],
                model=self.config["model"],
                use_enhanced=self.config["enable_enhanced"]
            )
            
            streaming_config = speech.StreamingRecognitionConfig(
                config=config,
                interim_results=self.config["interim_results"]
            )
            
            print("🎤 Agent Lee is listening with real-time streaming...")
            print("Press Ctrl+C to stop")
            
            requests = (
                speech.StreamingRecognizeRequest(audio_content=chunk)
                for chunk in mic_stream
            )
            
            responses = self.speech_client.streaming_recognize(
                config=streaming_config,
                requests=requests
            )
            
            for response in responses:
                if not self.running:
                    break
                    
                for result in response.results:
                    if not result.is_final:
                        continue
                    
                    for alt in result.alternatives:
                        confidence = alt.confidence
                        
                        if hasattr(alt, 'words') and alt.words:
                            # Process with speaker diarization
                            self.process_diarized_words(alt.words, confidence)
                        else:
                            # Process without diarization
                            speaker_name = self.prompt_speaker_name("0")
                            self.handle_phrase(speaker_name, alt.transcript, confidence)
                            
        except KeyboardInterrupt:
            print("\n🛑 Stopping voice loop...")
        except Exception as e:
            print(f"❌ Streaming recognition failed: {e}")
            self.run_fallback_recognition()
    
    def process_diarized_words(self, words, confidence: float):
        """Process words with speaker diarization"""
        current_speaker = None
        current_phrase = ""
        
        for word_info in words:
            if word_info.speaker_tag != current_speaker:
                if current_speaker is not None and current_phrase.strip():
                    speaker_name = self.prompt_speaker_name(str(current_speaker))
                    self.handle_phrase(speaker_name, current_phrase.strip(), confidence)
                
                current_speaker = word_info.speaker_tag
                current_phrase = word_info.word
            else:
                current_phrase += " " + word_info.word
        
        # Handle final phrase
        if current_speaker is not None and current_phrase.strip():
            speaker_name = self.prompt_speaker_name(str(current_speaker))
            self.handle_phrase(speaker_name, current_phrase.strip(), confidence)
    
    def run_fallback_recognition(self):
        """Fallback recognition using basic speech_recognition"""
        try:
            import speech_recognition as sr
            
            recognizer = sr.Recognizer()
            mic = sr.Microphone()
            
            print("🎤 Using fallback voice recognition (no diarization)")
            print("Press Ctrl+C to stop")
            
            with mic as source:
                recognizer.adjust_for_ambient_noise(source)
                print("🎤 Adjusted for ambient noise")
            
            speaker_name = self.prompt_speaker_name("fallback")
            
            while self.running:
                try:
                    with mic as source:
                        audio = recognizer.listen(source, timeout=1, phrase_time_limit=5)
                    
                    text = recognizer.recognize_google(audio)
                    self.handle_phrase(speaker_name, text)
                    
                except sr.WaitTimeoutError:
                    pass
                except sr.UnknownValueError:
                    pass
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"🎤 Recognition error: {e}")
                    time.sleep(1)
                    
        except ImportError:
            print("❌ No voice recognition available. Install: pip install SpeechRecognition")
        except KeyboardInterrupt:
            print("\n🛑 Stopping fallback voice loop...")
    
    def start(self):
        """Start the voice loop"""
        self.running = True
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🚀 Starting Agent Lee Voice Loop CLI")
        print(f"📋 Known speakers: {list(self.speaker_map.values())}")
        
        try:
            self.run_streaming_recognition()
        finally:
            self.cleanup()
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n📡 Received signal {signum}, shutting down...")
        self.running = False
    
    def cleanup(self):
        """Cleanup resources"""
        self.running = False
        
        if self.audio_stream:
            try:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
            except:
                pass
        
        self.save_speaker_map()
        print("🧹 Cleanup completed")

def main():
    parser = argparse.ArgumentParser(description="Agent Lee Voice Loop CLI")
    parser.add_argument("--config", help="Path to config file")
    parser.add_argument("--speakers", help="Path to speaker map file")
    parser.add_argument("--language", default="en-US", help="Language code (default: en-US)")
    parser.add_argument("--speaker-count", type=int, default=10, help="Max speaker count (default: 10)")
    parser.add_argument("--model", default="video", help="Speech model (default: video)")
    parser.add_argument("--enhanced", action="store_true", help="Use enhanced model")
    parser.add_argument("--interim", action="store_true", help="Show interim results")
    parser.add_argument("--diagnostics", action="store_true", help="Enable diagnostics mode")
    
    args = parser.parse_args()
    
    # Override global file paths if specified
    global SPEAKER_MAP_FILE, CONFIG_FILE
    if args.speakers:
        SPEAKER_MAP_FILE = args.speakers
    if args.config:
        CONFIG_FILE = args.config
    
    voice_loop = VoiceLoopCLI()
    
    # Override config with command line args
    if args.language:
        voice_loop.config["language_code"] = args.language
    if args.speaker_count:
        voice_loop.config["speaker_count"] = args.speaker_count
    if args.model:
        voice_loop.config["model"] = args.model
    if args.enhanced:
        voice_loop.config["enable_enhanced"] = True
    if args.interim:
        voice_loop.config["interim_results"] = True
    
    if args.diagnostics:
        print("🔍 Diagnostics Mode Enabled")
        print(f"Config: {voice_loop.config}")
        print(f"Speaker Map: {voice_loop.speaker_map}")
        print(f"Python Path: {sys.executable}")
        print(f"Working Directory: {os.getcwd()}")
    
    try:
        voice_loop.start()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("[DEPRECATED] This script is deprecated. Please use backend/voice_loop.py for unified CLI and Google STT support.")
    sys.exit(0)