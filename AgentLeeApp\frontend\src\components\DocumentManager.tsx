import React, { useState, useRef } from 'react';

interface UploadedFile {
  id: number;
  name: string;
  size: number;
  type: string;
  uploadDate: string;
  url: string;
}

interface DocumentManagerProps {
  onDocumentUpload?: (file: UploadedFile) => void;
  onDocumentDownload?: (file: UploadedFile) => void;
}

const DocumentManager: React.FC<DocumentManagerProps> = ({ onDocumentUpload, onDocumentDownload }) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setIsUploading(true);

    try {
      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('user_id', 'user');

        // Upload to backend
        const response = await fetch('http://localhost:8000/api/upload', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          const result = await response.json();
          const newFile: UploadedFile = {
            id: Date.now() + Math.random(),
            name: file.name,
            size: file.size,
            type: file.type,
            uploadDate: new Date().toISOString(),
            url: result.url || URL.createObjectURL(file)
          };

          setUploadedFiles(prev => [...prev, newFile]);
          
          if (onDocumentUpload) {
            onDocumentUpload(newFile);
          }

          console.log('✅ File uploaded:', file.name);
        } else {
          console.error('❌ Upload failed for:', file.name);
        }
      }
    } catch (error) {
      console.error('❌ Upload error:', error);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDownload = async (file: UploadedFile) => {
    try {
      if (file.url.startsWith('blob:')) {
        // Local file - direct download
        const link = document.createElement('a');
        link.href = file.url;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Server file - fetch and download
        const response = await fetch(file.url);
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
      }

      if (onDocumentDownload) {
        onDocumentDownload(file);
      }

      console.log('✅ File downloaded:', file.name);
    } catch (error) {
      console.error('❌ Download error:', error);
    }
  };

  const handleRemoveFile = (fileId: number) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string): string => {
    if (type.includes('pdf')) return '📄';
    if (type.includes('image')) return '🖼️';
    if (type.includes('text')) return '📝';
    if (type.includes('word')) return '📘';
    if (type.includes('excel') || type.includes('spreadsheet')) return '📊';
    if (type.includes('powerpoint') || type.includes('presentation')) return '📈';
    return '📎';
  };

  return (
    <div className="h-full flex flex-col">
      {/* Upload Area */}
      <div className="mb-4">
        <label htmlFor="document-file-input" className="sr-only">
          Upload documents (PDF, Word, Excel, PowerPoint, images, text files)
        </label>
        <input
          id="document-file-input"
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileUpload}
          className="hidden"
          accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.xlsx,.xls,.pptx,.ppt"
          aria-label="Upload documents (PDF, Word, Excel, PowerPoint, images, text files)"
        />

        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="w-full p-3 bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold rounded-lg hover:from-green-600 hover:to-blue-600 disabled:opacity-60 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2"
        >
          {isUploading ? (
            <>
              <span className="animate-spin">⏳</span> Uploading...
            </>
          ) : (
            <>
              <span className="text-lg">➕</span> Upload Documents
            </>
          )}
        </button>
      </div>

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="flex-1 overflow-y-auto">
          <h4 className="text-green-400 mb-3 text-sm font-medium">📁 Uploaded Documents ({uploadedFiles.length})</h4>
          {uploadedFiles.map(file => (
            <div key={file.id} className="flex justify-between items-center p-3 bg-white bg-opacity-5 rounded-lg mb-2 hover:bg-opacity-10 transition-colors">
              <div className="flex items-center gap-3 flex-1">
                <span className="text-2xl">{getFileIcon(file.type)}</span>
                <div className="flex-1">
                  <div className="text-gray-200 font-medium mb-1 break-words">{file.name}</div>
                  <div className="text-xs text-gray-400">
                    {formatFileSize(file.size)} • {new Date(file.uploadDate).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => handleDownload(file)}
                  className="p-2 bg-white bg-opacity-10 rounded hover:bg-opacity-20 hover:bg-green-500 transition-all"
                  title="Download"
                >
                  ⬇️
                </button>
                <button
                  type="button"
                  onClick={() => handleRemoveFile(file.id)}
                  className="p-2 bg-white bg-opacity-10 rounded hover:bg-opacity-20 hover:bg-red-500 transition-all"
                  title="Remove"
                >
                  🗑️
                </button>
              </div>
            </div>
          ))}
        </div>
      )}


    </div>
  );
};

export default DocumentManager;