# Agent Lee XTTS NGINX reverse proxy
server {
    listen 80;
    server_name _;

    location /api/tts {
        proxy_pass http://localhost:5002/api/tts;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        auth_basic "Restricted";
        auth_basic_user_file /etc/nginx/.htpasswd;
    }
}
