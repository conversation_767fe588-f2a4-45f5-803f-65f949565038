#!/usr/bin/env pwsh
# Agent Lee Integration Test Suite

Write-Host "🚀 Agent Lee Integration Test Suite" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Test 1: Service Health Checks
Write-Host "`n1. Testing Service Health..." -ForegroundColor Yellow

# Backend Health
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8000/health" -Method GET
    Write-Host "✅ Backend Health: $($health.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend Health: FAILED" -ForegroundColor Red
}

# LiveKit Server Check
try {
    $livekit = Test-NetConnection -ComputerName localhost -Port 7880 -WarningAction SilentlyContinue
    if ($livekit.TcpTestSucceeded) {
        Write-Host "✅ LiveKit Server: RUNNING" -ForegroundColor Green
    } else {
        Write-Host "❌ LiveKit Server: NOT RUNNING" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ LiveKit Server: ERROR" -ForegroundColor Red
}

# Frontend Server Check
try {
    $frontend = Test-NetConnection -ComputerName localhost -Port 3000 -WarningAction SilentlyContinue
    if ($frontend.TcpTestSucceeded) {
        Write-Host "✅ Frontend Server: RUNNING" -ForegroundColor Green
    } else {
        Write-Host "❌ Frontend Server: NOT RUNNING" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Frontend Server: ERROR" -ForegroundColor Red
}

# Test 2: Authentication Pipeline
Write-Host "`n2. Testing Authentication Pipeline..." -ForegroundColor Yellow

try {
    $loginBody = @{username="admin"; password="changeme"} | ConvertTo-Json
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/login" -Method POST -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.token
    Write-Host "✅ Authentication: SUCCESS" -ForegroundColor Green
    Write-Host "   Token: $($token.Substring(0,20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Authentication: FAILED" -ForegroundColor Red
    $token = $null
}

# Test 3: LiveKit Token Generation
Write-Host "`n3. Testing LiveKit Token Generation..." -ForegroundColor Yellow

try {
    $tokenBody = @{identity="test_user"; room="test_room"} | ConvertTo-Json
    $tokenResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/livekit/token" -Method POST -Body $tokenBody -ContentType "application/json"
    Write-Host "✅ LiveKit Token: SUCCESS" -ForegroundColor Green
    Write-Host "   Server URL: $($tokenResponse.server_url)" -ForegroundColor Gray
    Write-Host "   Room: $($tokenResponse.room)" -ForegroundColor Gray
} catch {
    Write-Host "❌ LiveKit Token: FAILED" -ForegroundColor Red
}

# Test 4: Chat Pipeline
Write-Host "`n4. Testing Chat Pipeline..." -ForegroundColor Yellow

try {
    $chatBody = @{message="Hello Agent Lee, this is a test"; user_id="test_user"} | ConvertTo-Json
    $chatResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/chat" -Method POST -Body $chatBody -ContentType "application/json"
    Write-Host "✅ Chat Pipeline: SUCCESS" -ForegroundColor Green
    Write-Host "   Response: $($chatResponse.response.Substring(0,50))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Chat Pipeline: FAILED" -ForegroundColor Red
}

# Test 5: MCP Tool Pipeline
Write-Host "`n5. Testing MCP Tool Pipeline..." -ForegroundColor Yellow

if ($token) {
    try {
        $mcpBody = @{tool="weather"; query="current weather"} | ConvertTo-Json
        $mcpResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/mcp/dispatch" -Method POST -Body $mcpBody -ContentType "application/json" -Headers @{Authorization="Bearer $token"}
        Write-Host "✅ MCP Pipeline: SUCCESS" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ MCP Pipeline: PARTIAL (Auth works, tool execution issues)" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ MCP Pipeline: SKIPPED (No auth token)" -ForegroundColor Red
}

# Test 6: Frontend Integration
Write-Host "`n6. Testing Frontend Integration..." -ForegroundColor Yellow

try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend Loading: SUCCESS" -ForegroundColor Green
        if ($frontendResponse.Content -match "Agent Lee") {
            Write-Host "✅ Frontend Content: VALID" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Frontend Content: MISSING AGENT LEE" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ Frontend Integration: FAILED" -ForegroundColor Red
}

Write-Host "`n🎯 Integration Test Complete!" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Summary
Write-Host "`n📊 SYSTEM STATUS SUMMARY:" -ForegroundColor Cyan
Write-Host "• LiveKit Server: ws://localhost:7880" -ForegroundColor White
Write-Host "• Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "• Frontend UI: http://localhost:3000" -ForegroundColor White
Write-Host "`n🎮 Ready for testing!" -ForegroundColor Green