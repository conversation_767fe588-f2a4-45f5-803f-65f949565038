#!/usr/bin/env python3
"""
Quick Frontend Tests for <PERSON> Lee
"""

import requests
import time
from datetime import datetime

class FrontendTester:
    def __init__(self):
        self.frontend_url = 'http://localhost:3000/public/'
        self.results = []
        
    def log_test(self, test_name, passed, details=''):
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        if passed:
            print(f'✅ {test_name}')
        else:
            print(f'❌ {test_name}')
            if details:
                print(f'   Details: {details}')

    def test_frontend_loads(self):
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                if '<PERSON> Lee' in response.text and 'root' in response.text:
                    self.log_test('Frontend Loads', True)
                else:
                    self.log_test('Frontend Loads', False, 'HTML content missing expected elements')
            else:
                self.log_test('Frontend Loads', False, f'Status: {response.status_code}')
        except Exception as e:
            self.log_test('Frontend Loads', False, str(e))

    def test_static_assets(self):
        try:
            # Test if we can access the directory listing (since it's a static server)
            response = requests.get('http://localhost:3000/', timeout=5)
            if response.status_code == 200:
                if 'public' in response.text and 'src' in response.text:
                    self.log_test('Static Assets Available', True)
                else:
                    self.log_test('Static Assets Available', False, 'Directory structure not as expected')
            else:
                self.log_test('Static Assets Available', False, f'Status: {response.status_code}')
        except Exception as e:
            self.log_test('Static Assets Available', False, str(e))

    def test_html_structure(self):
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                html = response.text
                required_elements = ['<div id="root">', '<noscript>', 'Agent Lee']
                missing_elements = [elem for elem in required_elements if elem not in html]
                
                if not missing_elements:
                    self.log_test('HTML Structure', True)
                else:
                    self.log_test('HTML Structure', False, f'Missing: {missing_elements}')
            else:
                self.log_test('HTML Structure', False, f'Status: {response.status_code}')
        except Exception as e:
            self.log_test('HTML Structure', False, str(e))

    def test_server_response_time(self):
        try:
            start_time = time.time()
            response = requests.get(self.frontend_url, timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200 and response_time < 2.0:
                self.log_test('Server Response Time', True, f'{response_time:.2f}s')
            else:
                self.log_test('Server Response Time', False, f'Slow response: {response_time:.2f}s')
        except Exception as e:
            self.log_test('Server Response Time', False, str(e))

    def run_tests(self):
        print('🌐 Running Frontend Tests...')
        print('=' * 50)
        
        self.test_frontend_loads()
        self.test_static_assets()
        self.test_html_structure()
        self.test_server_response_time()
        
        print('=' * 50)
        passed = sum(1 for r in self.results if r['passed'])
        total = len(self.results)
        print(f'📊 Results: {passed}/{total} tests passed')
        
        if passed == total:
            print('🎉 All frontend tests passed!')
            print(f'🌐 Frontend is available at: {self.frontend_url}')
            return True
        else:
            print('⚠️  Some frontend tests failed')
            return False

if __name__ == '__main__':
    tester = FrontendTester()
    success = tester.run_tests()
    exit(0 if success else 1)
