from flask import Flask, request, send_file
from TTS.api import TTS
import uuid
import os

# Ensure /tmp exists
os.makedirs("/tmp", exist_ok=True)

# Initialize Flask
app = Flask(__name__)

# Load XTTS model
tts = TTS(
    model_name="tts_models/multilingual/multi-dataset/xtts_v2",
    progress_bar=False,
    gpu=False  # Change to True if using GPU
)

@app.route("/api/tts", methods=["POST"])
def generate_tts():
    data = request.get_json()
    text = data.get("text", "<PERSON> Lee is ready.")

    # Generate unique filename
    out_path = f"/tmp/{uuid.uuid4()}.wav"

    # Generate TTS audio file
    tts.tts_to_file(text=text, file_path=out_path)

    # Serve the file as response
    return send_file(out_path, mimetype="audio/wav")

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5002)
