import sys

print('Python version:', sys.version)

# Test PyAudio
try:
    import pyaudio
    print('✅ PyAudio available')
    p = pyaudio.PyAudio()
    device_count = p.get_device_count()
    print(f'Audio devices found: {device_count}')
    for i in range(min(3, device_count)):
        info = p.get_device_info_by_index(i)
        if info['maxInputChannels'] > 0:
            print(f'  Input device {i}: {info["name"]}')
    p.terminate()
except ImportError:
    print('❌ PyAudio not available')
except Exception as e:
    print(f'⚠️ PyAudio error: {e}')

# Test SpeechRecognition
try:
    import speech_recognition as sr
    print('✅ SpeechRecognition available')
    print(f'SpeechRecognition version: {sr.__version__}')
except ImportError:
    print('❌ SpeechRecognition not available')
except Exception as e:
    print(f'⚠️ SpeechRecognition error: {e}')

# Test Google Cloud Speech (optional)
try:
    from google.cloud import speech_v1p1beta1
    print('✅ Google Cloud Speech available')
except ImportError:
    print('ℹ️ Google Cloud Speech not available (optional)')
except Exception as e:
    print(f'⚠️ Google Cloud Speech error: {e}')
