#
# phone_mcp.py — <PERSON>'s Phone Control Plugin (via phone-mcp)
#
# This MCP tool allows <PERSON> to:
# • Place and manage calls
# • Send SMS
# • Interact with apps, contacts, and system UI
# • Execute ADB-based phone automation
#
# Dependencies:
# • phone-mcp (https://github.com/hao-cyber/phone-mcp)
# • Android phone connected with USB debugging enabled
# • ADB installed in Docker (see Dockerfile)
#
# Author: Agent <PERSON>
#

import subprocess
import logging
import sys
import json
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("phone_mcp")

# Supported subtools and required args
SUPPORTED_TOOLS = {
    "call": ["number"],
    "sms": ["number", "message"],
    "contact": ["name", "number"],
    "screenshot": [],
    "launch_app": ["package"],
    "stop_app": ["package"],
    "tap": ["x", "y"],
    "swipe": ["x1", "y1", "x2", "y2"],
    "browser": ["url"],
}

def validate_args(subtool, args):
    """Validate arguments for each subtool."""
    if subtool in ["call", "sms", "contact"]:
        number = args.get("number")
        if number and not re.match(r'^[+]?[0-9]{7,15}$', number):
            raise ValueError(f"Invalid phone number: {number}")
    if subtool == "tap":
        try:
            x, y = int(args["x"]), int(args["y"])
            if not (0 <= x <= 1080 and 0 <= y <= 1920):  # Assuming common screen size
                raise ValueError("Tap coordinates out of bounds")
        except (ValueError, KeyError):
            raise ValueError("Invalid tap coordinates")
    if subtool == "swipe":
        try:
            x1, y1, x2, y2 = int(args["x1"]), int(args["y1"]), int(args["x2"]), int(args["y2"])
            if not all(0 <= coord <= 1920 for coord in [x1, y1, x2, y2]):
                raise ValueError("Swipe coordinates out of bounds")
        except (ValueError, KeyError):
            raise ValueError("Invalid swipe coordinates")
    if subtool == "browser":
        url = args.get("url")
        if url and not re.match(r'^https?://', url):
            raise ValueError(f"Invalid URL: {url}")
    # Add more validations as needed

def run(task, speaker):
    """
    Executes phone automation via phone-mcp using subtool and arguments.

    Args:
        task (dict): {
            "tool": "phone_mcp",
            "subtool": "call",  # or sms, screenshot, etc.
            "args": { "number": "1234567890" }
        }
        speaker (str): Speaker ID or name

    Returns:
        dict: Result or error message
    """
    try:
        subtool = task.get("subtool")
        args = task.get("args", {})

        if not subtool:
            return {"error": "No subtool specified."}
        if subtool not in SUPPORTED_TOOLS:
            return {"error": f"Subtool '{subtool}' not supported."}

        # Validate required args
        missing = [arg for arg in SUPPORTED_TOOLS[subtool] if arg not in args]
        if missing:
            return {"error": f"Missing required argument(s): {', '.join(missing)}"}

        # Additional validation
        validate_args(subtool, args)

        logger.info(f"👤 {speaker} | Running subtool: {subtool} with args: {args}")

        # Format CLI call: phone-cli <subtool> --arg1 val --arg2 val ...
        cmd = ["phone-cli", subtool]
        for k, v in args.items():
            cmd.append(f"--{k}={v}")  # Note: subprocess list prevents injection

        logger.debug(f"Executing command: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"Command successful: {result.stdout.strip()}")
            return {"status": "OK", "output": result.stdout.strip()}
        else:
            logger.error(f"Command failed (code {result.returncode}): {result.stderr.strip()}")
            return {
                "error": f"phone-cli failed with code {result.returncode}",
                "stderr": result.stderr.strip()
            }

    except ValueError as ve:
        logger.warning(f"Validation error: {ve}")
        return {"error": str(ve)}
    except Exception as e:
        logger.exception("Unexpected error in phone_mcp")
        return {"error": str(e)}

# 🧪 CLI test
if __name__ == "__main__":
    """
    Usage:
    python phone_mcp.py '{"tool": "phone_mcp", "subtool": "call", "args": {"number": "1234567890"}}'
    """
    if len(sys.argv) != 2:
        print("❌ Usage: python phone_mcp.py '<task-json>'")
        sys.exit(1)

    task_json = sys.argv[1]
    try:
        task = json.loads(task_json)
        result = run(task, speaker="CLI-Tester")
        print("✅ Result:", json.dumps(result, indent=2))
    except json.JSONDecodeError:
        print("❌ Invalid JSON.")
