/* App.css - Enhanced styles for Agent <PERSON> */

.App {
  text-align: center;
  background-color: #0f172a; /* Tailwind slate-900 */
  color: white;
  font-family: 'Inter', sans-serif;
  min-height: 100vh;
}

/* Voice Waveform Animation */
@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(0.3);
  }
  20% {
    transform: scaleY(1);
  }
}

.wave-bar {
  display: inline-block;
  width: 6px;
  height: 40px;
  margin: 0 2px;
  background: linear-gradient(180deg, #3b82f6, #9333ea);
  border-radius: 4px;
  animation: wave 1.2s infinite ease-in-out;
}

/* Dynamic delay (apply via JS or inline) */
.wave-bar:nth-child(1) { animation-delay: -1.1s; }
.wave-bar:nth-child(2) { animation-delay: -1s; }
.wave-bar:nth-child(3) { animation-delay: -0.9s; }
.wave-bar:nth-child(4) { animation-delay: -0.8s; }
.wave-bar:nth-child(5) { animation-delay: -0.7s; }

/* Fancy gradient title with 3D shadow */
.agent-title {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 2px 2px 12px rgba(0, 0, 0, 0.7);
  letter-spacing: 0.05em;
  margin-bottom: 2rem;
}

/* Card glow hover effect */
.card-glow:hover {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5), 0 0 40px rgba(59, 130, 246, 0.4);
  transition: box-shadow 0.3s ease-in-out;
}

/* Optional file upload wrapper */
.upload-label {
  cursor: pointer;
  background-color: #2563eb; /* blue-600 */
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}
.upload-label:hover {
  background-color: #3b82f6; /* blue-500 */
}
