version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: nginx_auth
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/htpasswd:/etc/nginx/.htpasswd:ro
    restart: unless-stopped

  tts:
    build: ./xtts
    container_name: tts_server
    ports:
      - "5002:5002"
services:
  xtts:
    build: ./xtts
    env_file:
      - ../.env
    ports:
      - "7860:7860"
    volumes:
      - ./xtts:/app
    command: ["bash", "-c", "cd /app && ./generate_env_js.sh && python server.py"]

  backend:
    build: ./xtts
    command: ["python", "server.py"]
    env_file:
      - ../.env
    ports:
      - "5000:5000"

  nginx:
    image: nginx:latest
    ports:
      - "80:80"
    volumes:
      - ./assets:/usr/share/nginx/html/assets
      - ./index.html:/usr/share/nginx/html/index.html
      - ./nginx.conf:/etc/nginx/nginx.conf
