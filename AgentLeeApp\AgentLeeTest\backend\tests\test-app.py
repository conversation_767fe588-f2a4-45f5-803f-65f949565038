#!/usr/bin/env python3
"""
Simple test script for Agent Lee application
Tests basic functionality without complex build processes
"""

import requests
import json
import time
import sys

def test_backend_health():
    """Test if backend is responding"""
    try:
        response = requests.get('http://localhost:5000/health', timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend health check failed: {e}")
        return False

def test_api_endpoint():
    """Test basic API functionality"""
    try:
        response = requests.post(
            'http://localhost:5000/api/task/parse',
            headers={
                'Content-Type': 'application/json',
                'Authorization': 'Bearer changeme'
            },
            json={
                'speaker': 'test_user',
                'text': 'Hello, this is a test message'
            },
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ API endpoint test passed")
            return True
        else:
            print(f"❌ API endpoint test failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API endpoint test failed: {e}")
        return False

def test_frontend_access():
    """Test if frontend is accessible"""
    try:
        response = requests.get('http://localhost:80', timeout=5)
        if response.status_code == 200:
            print("✅ Frontend access test passed")
            return True
        else:
            print(f"❌ Frontend access test failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend access test failed: {e}")
        return False

def test_mcp_services():
    """Test MCP services"""
    services = [
        ('mcphub', 3000),
        ('aura', 8000),
        ('agentlee', 8020),
        ('phone', 8010),
        ('chrome', 9222),
        ('mobile', 9010),
        ('playwright', 3001),
        ('chart', 3020),
        ('xtts', 8080)
    ]
    
    results = []
    for service_name, port in services:
        try:
            response = requests.get(f'http://localhost:{port}', timeout=3)
            if response.status_code in [200, 404, 405]:  # Accept various responses
                print(f"✅ {service_name} service test passed")
                results.append(True)
            else:
                print(f"❌ {service_name} service test failed: {response.status_code}")
                results.append(False)
        except requests.exceptions.RequestException:
            print(f"⚠️  {service_name} service not responding (may be normal)")
            results.append(False)
    
    return any(results)  # At least one service should be working

def main():
    """Run all tests"""
    print("🧪 Starting Agent Lee Application Tests")
    print("=" * 50)
    
    tests = [
        ("Backend Health Check", test_backend_health),
        ("API Endpoint Test", test_api_endpoint),
        ("Frontend Access Test", test_frontend_access),
        ("MCP Services Test", test_mcp_services)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Agent Lee is ready to use.")
        return 0
    elif passed > 0:
        print("⚠️  Some tests passed. Application may work with limited functionality.")
        return 1
    else:
        print("❌ All tests failed. Please check your setup.")
        return 2

if __name__ == "__main__":
    sys.exit(main()) 