  return `🧰 Node Sandbox received: ${input}\n(Node simulation)`;
}
if (require.main === module) {
  const input = process.argv[2] || '';
  main(input).then(console.log);
}

import { MCPServer } from 'model-context-protocol';
import { NodeVM } from 'vm2';

export async function startServer(port = 3008) {
  const server = new MCPServer({ port });

  server.registerTool('runSnippet', {
    title: 'Node Sandbox',
    parameters: { type: 'object', properties: { code: { type: 'string' } }, required: ['code'] },
    returns: { type: 'object', properties: { result: { type: 'string' } } }
  }, async ({ code }) => {
    const vm = new NodeVM({ timeout: 1000, sandbox: {} });
    const result = vm.run(`module.exports = (async () => { ${code} })()`);
    return { result: String(await result) };
  });

  await server.listen();
  console.log(`NodeSandbox MCP server running on port ${port}`);
}
