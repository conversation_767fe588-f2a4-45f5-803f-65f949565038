from flask import Flask, request, jsonify
from flask_cors import CORS
from memory_db import save_to_memory, search_memory, save_session, load_sessions, get_speaker_stats
from dispatcher import dispatch, run_tool
import os
import threading
import time

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

@app.route('/api/task/parse', methods=['POST'])
def handle_task():
    """Enhanced task handler with multi-speaker support"""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        speaker = data.get('speaker', 'unknown')
        text = data.get('text', '')
        tool = data.get('tool')

        if not text:
            return jsonify({"error": "No text provided"}), 400

        # Save to memory and session history
        save_to_memory(speaker, text)
        save_session(speaker, text)

        # Dispatch to appropriate tool
        if tool:
            task = {"tool": tool, "input": text}
            result = dispatch(task, speaker)
        else:
            # Use legacy run_tool for backward compatibility
            result = run_tool(text, speaker)

        return result if hasattr(result, 'status_code') else jsonify({'response': result})

    except Exception as e:
        return jsonify({"error": f"Server error: {str(e)}"}), 500

@app.route('/api/memory/search', methods=['GET'])
def memory_lookup():
    """Enhanced memory search with better filtering"""
    try:
        speaker = request.args.get('speaker')
        query = request.args.get('query')
        results = search_memory(speaker, query)
        return jsonify({'results': results})
    except Exception as e:
        return jsonify({"error": f"Memory search error: {str(e)}"}), 500

@app.route('/api/sessions', methods=['GET'])
def get_sessions():
    """Get all speaker sessions"""
    try:
        sessions = load_sessions()
        return jsonify(sessions)
    except Exception as e:
        return jsonify({"error": f"Session retrieval error: {str(e)}"}), 500

@app.route('/api/speakers/<speaker>/stats', methods=['GET'])
def speaker_stats(speaker):
    """Get statistics for a specific speaker"""
    try:
        stats = get_speaker_stats(speaker)
        return jsonify(stats)
    except Exception as e:
        return jsonify({"error": f"Stats error: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({"status": "OK", "service": "Agent Lee Backend"})

@app.route('/api/tools', methods=['GET'])
def list_tools():
    """List available tools"""
    try:
        from dispatcher import TOOL_REGISTRY
        return jsonify({"tools": TOOL_REGISTRY})
    except Exception as e:
        return jsonify({"error": f"Tool listing error: {str(e)}"}), 500

@app.route('/api/speakers/<speaker>/badges', methods=['GET'])
def get_speaker_badges(speaker):
    """Get badges for a specific speaker"""
    try:
        from mcp.badge_awarder import get_speaker_badges
        badges = get_speaker_badges(speaker)
        return jsonify(badges)
    except Exception as e:
        return jsonify({"error": f"Badge retrieval error: {str(e)}"}), 500

@app.route('/api/leaderboard', methods=['GET'])
def get_leaderboard():
    """Get badge leaderboard"""
    try:
        from mcp.badge_awarder import get_leaderboard
        limit = int(request.args.get('limit', 10))
        leaderboard = get_leaderboard(limit)
        return jsonify({"leaderboard": leaderboard})
    except Exception as e:
        return jsonify({"error": f"Leaderboard error: {str(e)}"}), 500

@app.route('/api/speakers/<speaker>/resume', methods=['POST'])
def generate_resume(speaker):
    """Generate resume for a speaker"""
    try:
        data = request.json or {}
        resume_type = data.get('type', 'standard')
        additional_info = data.get('additional_info', '')

        from mcp.resume_builder import run as build_resume
        task = {
            "input": additional_info,
            "type": resume_type
        }
        result = build_resume(task, speaker)
        return result
    except Exception as e:
        return jsonify({"error": f"Resume generation error: {str(e)}"}), 500

@app.route('/api/speakers/<speaker>/certificate', methods=['POST'])
def generate_certificate(speaker):
    """Generate certificate for a speaker"""
    try:
        data = request.json or {}
        achievement = data.get('achievement', 'Completion')
        description = data.get('description', '')

        from mcp.pdf_creator import create_certificate
        result = create_certificate(speaker, achievement, description)
        return result
    except Exception as e:
        return jsonify({"error": f"Certificate generation error: {str(e)}"}), 500

# Optional: Real-time voice processing (requires speech_recognition)
def background_voice_loop():
    """Background voice processing loop"""
    try:
        import speech_recognition as sr
        recognizer = sr.Recognizer()
        mic = sr.Microphone()

        print("🎤 Voice loop started. Listening for commands...")

        with mic as source:
            recognizer.adjust_for_ambient_noise(source)

        while True:
            try:
                with mic as source:
                    # Listen for audio with timeout
                    audio = recognizer.listen(source, timeout=1, phrase_time_limit=5)

                # Recognize speech
                command = recognizer.recognize_google(audio)
                speaker = "VoiceUser"  # Could be enhanced with speaker identification

                print(f"🎤 {speaker}: {command}")

                # Save and process command
                save_to_memory(speaker, command)
                save_session(speaker, command)

                # Dispatch to LLM chat for processing
                task = {"tool": "llm_chat", "input": command}
                result = dispatch(task, speaker)

                print(f"🤖 Response: {result}")

            except sr.WaitTimeoutError:
                # No speech detected, continue listening
                pass
            except sr.UnknownValueError:
                # Speech not understood
                print("🎤 Could not understand audio")
            except Exception as e:
                print(f"🎤 Voice loop error: {e}")
                time.sleep(1)

    except ImportError:
        print("🎤 Speech recognition not available. Install speech_recognition and pyaudio.")
    except Exception as e:
        print(f"🎤 Voice loop initialization error: {e}")

# Start voice loop in background (optional)
voice_loop_type = os.getenv('VOICE_LOOP_TYPE', 'none').lower()

if voice_loop_type == 'google':
    try:
        from voice_loop_google import start_voice_loop
        start_voice_loop()
        print("🎤 Google Voice Loop with diarization enabled")
    except ImportError:
        print("❌ Google Voice Loop not available. Install google-cloud-speech")
elif voice_loop_type == 'basic':
    voice_thread = threading.Thread(target=background_voice_loop, daemon=True)
    voice_thread.start()
    print("🎤 Basic voice processing enabled")
else:
    print("🎤 Voice processing disabled. Set VOICE_LOOP_TYPE=google or basic to enable")

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    app.run(host='0.0.0.0', port=port, debug=debug)
