version: '3.9'

services:
  xtts:
    build:
      context: ./xtts
      dockerfile: Dockerfile
    container_name: agentlee-xtts
    ports:
      - "5002:5002"
    restart: unless-stopped

  mcp-puppeteer:
    image: mcp/puppeteer
    container_name: mcp-puppeteer
    ports:
      - "5010:8080"
    restart: unless-stopped
    environment:
      DOCKER_CONTAINER: "true"

  mcp-ddg:
    image: mcp/duckduckgo
    container_name: mcp-duckduckgo
    ports:
      - "5011:8080"
    restart: unless-stopped
