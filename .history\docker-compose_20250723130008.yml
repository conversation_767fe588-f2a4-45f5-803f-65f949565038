
version: '3.8'

services:
  tts:
    build: ./xtts
    container_name: tts_server
    ports:
      - "5002:5002"
    environment:
      - CORS_ORIGINS=*
    restart: unless-stopped

  webhook:
    build: ./xtts
    container_name: webhook_server
    command: gunicorn -w 2 -b 0.0.0.0:8080 webhook_server:app
    ports:
      - "8080:8080"
    environment:
      - XTTS_API=http://tts:5002/api/tts
    depends_on:
      - tts
    restart: unless-stopped
