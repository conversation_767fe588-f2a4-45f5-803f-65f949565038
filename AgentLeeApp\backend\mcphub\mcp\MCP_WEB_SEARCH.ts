
// ...existing code...
import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3006, ddgAppKey?: string) {
  const server = new MCPServer({ port });

  server.registerTool('webSearch', {
    title: 'Web Search',
    description: 'Search the web and return top results',
    parameters: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        engine: { type: 'string', default: 'duckduckgo' }
      },
      required: ['query']
    },
    returns: {
      type: 'object',
      properties: {
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              snippet: { type: 'string' },
              url: { type: 'string' }
            }
          }
        }
      }
    }
  }, async ({ query, engine }) => {
    if (engine === 'duckduckgo') {
      const res = await axios.get(`https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json`);
      const results = (res.data.RelatedTopics || []).slice(0, 5).map((r: any) => ({
        title: r.Text,
        snippet: r.Text,
        url: r.FirstURL
      }));
      return { results };
    } else {
      throw new Error('Only duckduckgo engine is supported currently');
    }
  });

  await server.listen();
  console.log(`WebSearch MCP server running on port ${port}`);
}
