# Base XTTS ML image
FROM xtts-ml-base:latest
# Set working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Pre-download XTTS model inside the image (commented out due to JSON parsing issues)
# RUN python -c "from TTS.api import TTS; TTS(model_name='tts_models/multilingual/multi-dataset/xtts_v2')"

# Ensure temp folder exists
RUN mkdir -p /tmp

# Copy project files
COPY . .

# Expose XTTS HTTP API port (Fly.io compatible)
EXPOSE 7860

# Start the TTS server on port 7860
CMD ["python", "server.py"]

# Add build args and envs for all required variables
ARG FIREBASE_API_KEY=""
ENV FIREBASE_API_KEY=${FIREBASE_API_KEY}
ARG FIREBASE_DATABASE_URL=""
ENV FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
ARG FIREBASE_STORAGE_BUCKET=""
ENV FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
ARG OPENROUTER_API_KEY=""
ENV OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
ARG agent_lee_public_url=""
ENV agent_lee_public_url=${agent_lee_public_url}
ARG MCP_INDEXEDDB_NAMESPACE=""
ENV MCP_INDEXEDDB_NAMESPACE=${MCP_INDEXEDDB_NAMESPACE}
ARG MCP_TASK_DB=""
ENV MCP_TASK_DB=${MCP_TASK_DB}
ARG MCP_STATUS_DB=""
ENV MCP_STATUS_DB=${MCP_STATUS_DB}
ARG MCP_LOG_DB=""
ENV MCP_LOG_DB=${MCP_LOG_DB}
