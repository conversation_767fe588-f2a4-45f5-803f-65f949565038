#!/usr/bin/env python3
"""
Comprehensive MCP Tools Test Suite
Tests all 25+ MCP tools to ensure they're working perfectly with the MCP hub
"""

import requests
import json
import time
import sys
import os
from datetime import datetime
import concurrent.futures
import threading

class MCPTestSuite:
    def __init__(self, mcp_hub_url="http://localhost:3000"):
        self.mcp_hub_url = mcp_hub_url.rstrip('/')
        self.test_results = []
        self.failed_tests = []
        self.passed_tests = []
        self.lock = threading.Lock()
        
        # All 25+ MCP tools to test
        self.mcp_tools = {
            # Core Communication & AI
            'weather': {
                'test_data': {'location': 'Dallas', 'input': 'weather in Dallas'},
                'expected_keys': ['response', 'weather_data'],
                'description': 'Weather information service'
            },
            'llm_response': {
                'test_data': {'input': 'Hello, how are you today?'},
                'expected_keys': ['response'],
                'description': 'LLM chat and responses'
            },
            'llm_chat': {
                'test_data': {'input': 'What is the capital of France?'},
                'expected_keys': ['response'],
                'description': 'LLM chat interface'
            },
            'llm_tools': {
                'test_data': {'input': 'Analyze this text: Hello world'},
                'expected_keys': ['response'],
                'description': 'Extended LLM tools'
            },
            
            # Document & File Processing
            'document_tools': {
                'test_data': {'input': 'Create a summary document', 'type': 'summary'},
                'expected_keys': ['response'],
                'description': 'Document creation and processing'
            },
            'doc_analyzer': {
                'test_data': {'input': 'Analyze document content', 'content': 'Sample document text'},
                'expected_keys': ['response'],
                'description': 'Document analysis and insights'
            },
            'file_tools': {
                'test_data': {'input': 'list files', 'action': 'list'},
                'expected_keys': ['response'],
                'description': 'File operations and management'
            },
            'file_backup': {
                'test_data': {'input': 'backup important files', 'path': '/tmp'},
                'expected_keys': ['response'],
                'description': 'File backup operations'
            },
            
            # Communication Tools
            'email_sender': {
                'test_data': {'input': 'send <NAME_EMAIL>', 'to': '<EMAIL>', 'subject': 'Test', 'body': 'Test message'},
                'expected_keys': ['response'],
                'description': 'Email sending service'
            },
            'sms_sender': {
                'test_data': {'input': 'send SMS message', 'message': 'Test SMS', 'to': '+1234567890'},
                'expected_keys': ['response'],
                'description': 'SMS and messaging service'
            },
            'communication_tools': {
                'test_data': {'input': 'send notification', 'type': 'notification', 'message': 'Test notification'},
                'expected_keys': ['response'],
                'description': 'General communication tools'
            },
            
            # Calendar & Scheduling
            'calendar_event': {
                'test_data': {'input': 'create meeting tomorrow', 'title': 'Test Meeting', 'date': '2024-01-15', 'time': '10:00'},
                'expected_keys': ['response'],
                'description': 'Calendar event management'
            },
            
            # Media & Content
            'image_gen': {
                'test_data': {'input': 'generate image of a sunset', 'prompt': 'beautiful sunset over mountains'},
                'expected_keys': ['response'],
                'description': 'AI image generation'
            },
            'camera_tools': {
                'test_data': {'input': 'take photo', 'action': 'capture'},
                'expected_keys': ['response'],
                'description': 'Camera and photo operations'
            },
            'screenshot': {
                'test_data': {'input': 'take screenshot', 'action': 'capture'},
                'expected_keys': ['response'],
                'description': 'Screen capture functionality'
            },
            'youtube_meta': {
                'test_data': {'input': 'get YouTube video info', 'url': 'https://youtube.com/watch?v=dQw4w9WgXcQ'},
                'expected_keys': ['response'],
                'description': 'YouTube metadata extraction'
            },
            
            # Voice & Audio
            'voice_embedder': {
                'test_data': {'input': 'process voice data', 'audio_data': 'sample_audio'},
                'expected_keys': ['response'],
                'description': 'Voice processing and embedding'
            },
            'note_taker': {
                'test_data': {'input': 'take notes from speech', 'audio': 'sample speech'},
                'expected_keys': ['response'],
                'description': 'Voice-to-text transcription'
            },
            
            # Analysis & Intelligence
            'ai_toolkit': {
                'test_data': {'input': 'analyze data patterns', 'data': 'sample data set'},
                'expected_keys': ['response'],
                'description': 'AI analysis toolkit'
            },
            'face_tracker': {
                'test_data': {'input': 'analyze face in image', 'image_data': 'sample_image'},
                'expected_keys': ['response'],
                'description': 'Face detection and tracking'
            },
            'sensor_bridge': {
                'test_data': {'input': 'read sensor data', 'sensor_type': 'temperature'},
                'expected_keys': ['response'],
                'description': 'IoT sensor data bridge'
            },
            
            # System & Control
            'task_router': {
                'test_data': {'input': 'route this task', 'task_type': 'general'},
                'expected_keys': ['response'],
                'description': 'Task routing and orchestration'
            },
            'phone_mcp': {
                'test_data': {'input': 'check phone status', 'action': 'status'},
                'expected_keys': ['response'],
                'description': 'Phone control via ADB'
            },
            'mobile_control': {
                'test_data': {'input': 'mobile device control', 'action': 'status'},
                'expected_keys': ['response'],
                'description': 'Mobile device automation'
            },
            
            # Web Automation
            'chrome_automation': {
                'test_data': {'input': 'automate browser task', 'url': 'https://example.com'},
                'expected_keys': ['response'],
                'description': 'Chrome browser automation'
            },
            'playwright_automation': {
                'test_data': {'input': 'web automation task', 'url': 'https://example.com'},
                'expected_keys': ['response'],
                'description': 'Playwright web automation'
            },
            
            # Visualization & Charts
            'chart_generator': {
                'test_data': {'input': 'create chart', 'data': [1, 2, 3, 4, 5], 'type': 'line'},
                'expected_keys': ['response'],
                'description': 'Chart and graph generation'
            },
            
            # Gamification
            'badge_awarder': {
                'test_data': {'input': 'award badge', 'badge_type': 'achievement', 'user': 'test_user'},
                'expected_keys': ['response'],
                'description': 'Badge and achievement system'
            },
            
            # 2025 AI Models Integration
            'hunyuan_video': {
                'test_data': {'input': 'Create a video of a sunset over mountains', 'mode': 'video', 'duration': 5},
                'expected_keys': ['response'],
                'description': 'HunyuanVideo-13B text-to-video generation'
            },
            'multimodal_ai': {
                'test_data': {'input': 'Create a professional presentation about AI trends', 'type': 'presentation'},
                'expected_keys': ['response'],
                'description': '2025 multimodal AI pipeline (HunyuanVideo + HiDream + Gemini)'
            },
            
            # Multilingual & Voice Command Support
            'multilingual_agent': {
                'test_data': {'input': 'Hello, can you speak Spanish?', 'type': 'chat'},
                'expected_keys': ['response'],
                'description': 'Agent Lee multilingual communication (95+ languages)'
            },
            'voice_command_processor': {
                'test_data': {'type': 'text_to_speech', 'text': 'Hello Agent Lee', 'language': 'en'},
                'expected_keys': ['response'],
                'description': 'Voice command processing and TTS in multiple languages'
            }
        }
        
    def log(self, message, test_type='info'):
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'type': test_type,
            'message': message
        }
        
        with self.lock:
            self.test_results.append(log_entry)
        
        color_codes = {
            'info': '\033[94m',      # Blue
            'success': '\033[92m',   # Green
            'warning': '\033[93m',   # Yellow
            'error': '\033[91m',     # Red
            'test': '\033[95m'       # Magenta
        }
        
        color = color_codes.get(test_type, '\033[0m')
        print(f"{color}[{timestamp}] {test_type.upper()}: {message}\033[0m")

    def test_mcp_hub_health(self):
        """Test MCP hub health and availability"""
        try:
            response = requests.get(f"{self.mcp_hub_url}/", timeout=10)
            response.raise_for_status()
            
            # Check if we get any response (even plain text)
            if response.status_code == 200:
                self.log("✅ MCP Hub is running and accessible", 'success')
                return True
            else:
                self.log(f"❌ MCP Hub health check failed: {response.status_code}", 'error')
                return False
                
        except Exception as e:
            self.log(f"❌ MCP Hub connection failed: {e}", 'error')
            return False

    def test_mcp_tool_list(self):
        """Test MCP hub tool listing"""
        try:
            response = requests.get(f"{self.mcp_hub_url}/api/tools", timeout=10)
            response.raise_for_status()
            
            data = response.json()
            tools = data.get('tools', [])
            count = data.get('count', 0)
            
            self.log(f"✅ MCP Hub reports {count} tools available: {', '.join(tools[:10])}{'...' if len(tools) > 10 else ''}", 'success')
            return tools
            
        except Exception as e:
            self.log(f"❌ Failed to get tool list: {e}", 'error')
            return []

    def test_single_mcp_tool(self, tool_name, tool_config):
        """Test a single MCP tool"""
        try:
            self.log(f"🧪 Testing {tool_name}: {tool_config['description']}", 'test')
            
            # Prepare test data
            test_data = tool_config['test_data'].copy()
            test_data['tool'] = tool_name
            test_data['speaker'] = 'test_user'
            
            # Make request to MCP hub
            response = requests.post(
                f"{self.mcp_hub_url}/api/tools/execute",
                json=test_data,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check for expected keys or success indicators
                has_expected_content = (
                    any(key in data for key in tool_config.get('expected_keys', [])) or
                    data.get('success') == True or
                    'response' in data or
                    'data' in data or
                    'mock' in data  # Accept mock responses
                )
                
                if has_expected_content:
                    with self.lock:
                        self.passed_tests.append(tool_name)
                    
                    response_preview = str(data.get('response', data.get('data', data)))[:100]
                    mock_indicator = " (MOCK)" if data.get('mock') else ""
                    self.log(f"✅ {tool_name} working{mock_indicator}: {response_preview}...", 'success')
                    return True
                else:
                    with self.lock:
                        self.failed_tests.append((tool_name, f"Missing expected content: {data}"))
                    self.log(f"⚠️  {tool_name} responded but missing expected content", 'warning')
                    return False
            else:
                with self.lock:
                    self.failed_tests.append((tool_name, f"HTTP {response.status_code}: {response.text}"))
                self.log(f"❌ {tool_name} failed with status {response.status_code}", 'error')
                return False
                
        except requests.exceptions.Timeout:
            with self.lock:
                self.failed_tests.append((tool_name, "Request timeout"))
            self.log(f"⏰ {tool_name} timed out", 'warning')
            return False
        except Exception as e:
            with self.lock:
                self.failed_tests.append((tool_name, str(e)))
            self.log(f"❌ {tool_name} error: {e}", 'error')
            return False

    def test_all_tools_sequential(self):
        """Test all tools one by one"""
        self.log("🔄 Testing all MCP tools sequentially...", 'info')
        
        for tool_name, tool_config in self.mcp_tools.items():
            self.test_single_mcp_tool(tool_name, tool_config)
            time.sleep(0.5)  # Small delay between tests

    def test_all_tools_parallel(self, max_workers=5):
        """Test all tools in parallel for faster execution"""
        self.log(f"🚀 Testing all MCP tools in parallel (max {max_workers} workers)...", 'info')
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_tool = {
                executor.submit(self.test_single_mcp_tool, tool_name, tool_config): tool_name
                for tool_name, tool_config in self.mcp_tools.items()
            }
            
            for future in concurrent.futures.as_completed(future_to_tool):
                tool_name = future_to_tool[future]
                try:
                    future.result()
                except Exception as e:
                    self.log(f"❌ {tool_name} parallel test exception: {e}", 'error')

    def test_stress_test(self, iterations=10):
        """Stress test the MCP hub with multiple requests"""
        self.log(f"💪 Running stress test with {iterations} iterations...", 'info')
        
        # Pick a few reliable tools for stress testing
        stress_tools = ['weather', 'llm_response', 'task_router']
        
        for i in range(iterations):
            for tool_name in stress_tools:
                if tool_name in self.mcp_tools:
                    self.test_single_mcp_tool(tool_name, self.mcp_tools[tool_name])
            
            if i % 3 == 0:
                self.log(f"Stress test progress: {i+1}/{iterations}", 'info')

    def generate_report(self):
        """Generate comprehensive test report"""
        total_tools = len(self.mcp_tools)
        passed_count = len(self.passed_tests)
        failed_count = len(self.failed_tests)
        success_rate = (passed_count / total_tools * 100) if total_tools > 0 else 0
        
        self.log("\n" + "="*60, 'info')
        self.log("📊 MCP TOOLS TEST REPORT", 'info')
        self.log("="*60, 'info')
        self.log(f"Total Tools Tested: {total_tools}", 'info')
        self.log(f"✅ Passed: {passed_count}", 'success')
        self.log(f"❌ Failed: {failed_count}", 'error')
        self.log(f"📈 Success Rate: {success_rate:.1f}%", 'info')
        
        if self.passed_tests:
            self.log(f"\n✅ WORKING TOOLS ({len(self.passed_tests)}):", 'success')
            for tool in sorted(self.passed_tests):
                self.log(f"  • {tool}: {self.mcp_tools[tool]['description']}", 'success')
        
        if self.failed_tests:
            self.log(f"\n❌ FAILED TOOLS ({len(self.failed_tests)}):", 'error')
            for tool, error in self.failed_tests:
                self.log(f"  • {tool}: {error}", 'error')
        
        # Save detailed report
        report = {
            'timestamp': datetime.now().isoformat(),
            'mcp_hub_url': self.mcp_hub_url,
            'summary': {
                'total_tools': total_tools,
                'passed': passed_count,
                'failed': failed_count,
                'success_rate': success_rate
            },
            'passed_tools': self.passed_tests,
            'failed_tools': [{'tool': tool, 'error': error} for tool, error in self.failed_tests],
            'all_results': self.test_results
        }
        
        with open('mcp_tools_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        self.log("📄 Detailed report saved to mcp_tools_test_report.json", 'info')
        
        return {
            'total': total_tools,
            'passed': passed_count,
            'failed': failed_count,
            'success_rate': success_rate
        }

    def run_comprehensive_test(self):
        """Run all tests in sequence"""
        self.log("🚀 Starting Comprehensive MCP Tools Test Suite", 'info')
        self.log(f"MCP Hub URL: {self.mcp_hub_url}", 'info')
        self.log(f"Total Tools to Test: {len(self.mcp_tools)}", 'info')
        
        # 1. Test MCP Hub health
        if not self.test_mcp_hub_health():
            self.log("❌ MCP Hub is not available. Cannot proceed with tests.", 'error')
            return False
        
        # 2. Test tool listing
        available_tools = self.test_mcp_tool_list()
        
        # 3. Test all tools
        self.test_all_tools_parallel(max_workers=3)  # Moderate parallelism
        
        # 4. Generate report
        results = self.generate_report()
        
        # 5. Final status
        if results['success_rate'] >= 80:
            self.log("🎉 MCP Tools Test Suite PASSED! Most tools are working correctly.", 'success')
            return True
        else:
            self.log("⚠️  MCP Tools Test Suite completed with issues. Check failed tools.", 'warning')
            return False

def main():
    """Main test runner"""
    mcp_hub_url = os.environ.get('MCP_HUB_URL', 'http://localhost:3000')
    
    if len(sys.argv) > 1:
        mcp_hub_url = sys.argv[1]
    
    test_suite = MCPTestSuite(mcp_hub_url)
    
    try:
        success = test_suite.run_comprehensive_test()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()