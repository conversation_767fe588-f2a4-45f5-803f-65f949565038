# 🧠 Auto Task Generator — Infers Tool from Natural Language

import re

def infer_task_tool_from_text(text):
    """
    Analyze natural language text and infer the appropriate MCP tool to use
    Returns a task dictionary or None if no tool is detected
    """
    if not text:
        return None
        
    text_lower = text.lower().strip()
    
    # Email detection
    if any(keyword in text_lower for keyword in ["email", "send message", "mail", "notify"]):
        return {"tool": "email_sender", "input": text}
    
    # Calendar/scheduling detection
    if any(keyword in text_lower for keyword in ["calendar", "schedule", "meeting", "appointment", "book"]):
        return {"tool": "calendar_event", "input": text}
    
    # Summarization detection
    if any(keyword in text_lower for keyword in ["summarize", "recap", "summary", "brief", "overview"]):
        return {"tool": "summarize", "input": text}
    
    # Weather detection
    if any(keyword in text_lower for keyword in ["weather", "temperature", "forecast", "climate"]):
        return {"tool": "weather", "input": text}
    
    # PDF creation detection
    if any(keyword in text_lower for keyword in ["pdf", "document", "report", "create file"]):
        return {"tool": "pdf_creator", "input": text}
    
    # Image generation detection
    if any(keyword in text_lower for keyword in ["image", "picture", "generate", "create image", "draw"]):
        return {"tool": "image_gen", "input": text}
    
    # YouTube detection
    if any(keyword in text_lower for keyword in ["youtube", "video", "transcript"]):
        return {"tool": "youtube_meta", "input": text}
    
    # Badge/achievement detection
    if any(keyword in text_lower for keyword in ["badge", "achievement", "completed", "finished", "passed"]):
        return {"tool": "badge_awarder", "input": text}

    # Resume builder detection
    if any(keyword in text_lower for keyword in ["resume", "cv", "curriculum vitae", "build my resume", "create my resume"]):
        return {"tool": "resume_builder", "input": text}

    # File operations detection
    if any(keyword in text_lower for keyword in ["file", "upload", "download", "backup"]):
        return {"tool": "file_tools", "input": text}

    # Camera/vision detection
    if any(keyword in text_lower for keyword in ["camera", "photo", "picture", "capture", "vision"]):
        return {"tool": "camera_tools", "input": text}

    # Voice/audio detection
    if any(keyword in text_lower for keyword in ["voice", "audio", "record", "transcribe"]):
        return {"tool": "voice_embedder", "input": text}

    # Note taking detection
    if any(keyword in text_lower for keyword in ["note", "notes", "write down", "remember"]):
        return {"tool": "note_taker", "input": text}

    # Analysis detection
    if any(keyword in text_lower for keyword in ["analyze", "analysis", "examine", "study", "review"]):
        return {"tool": "doc_analyzer", "input": text}

    # Communication detection (advanced)
    if any(keyword in text_lower for keyword in ["communicate", "talk", "discuss", "conversation"]):
        return {"tool": "llm_com", "input": text}

    # Question/chat detection (catch-all for conversational input)
    question_indicators = ["what", "how", "why", "when", "where", "who", "can you", "please", "help"]
    if any(indicator in text_lower for indicator in question_indicators):
        return {"tool": "llm_chat", "input": text}

    # Default to chat for any unrecognized input
    return {"tool": "llm_chat", "input": text}

def extract_entities_from_text(text):
    """
    Extract useful entities from text for task parameters
    """
    entities = {}
    
    # Extract email addresses
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    if emails:
        entities['email'] = emails[0]
    
    # Extract time expressions
    time_patterns = [
        r'(\d{1,2}:\d{2}\s*(?:AM|PM|am|pm))',
        r'(\d{1,2}\s*(?:AM|PM|am|pm))',
        r'(tomorrow|today|next week|next month)',
        r'(\d{1,2}/\d{1,2})',
        r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)'
    ]
    
    for pattern in time_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            entities['time'] = match.group(1)
            break
    
    # Extract locations (simple city/state patterns)
    location_patterns = [
        r'(?:in|at|for)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
        r'([A-Z][a-z]+,\s*[A-Z]{2})',  # City, State
    ]
    
    for pattern in location_patterns:
        match = re.search(pattern, text)
        if match:
            entities['location'] = match.group(1)
            break
    
    return entities

def enhance_task_with_entities(task, text):
    """
    Enhance a task with extracted entities
    """
    if not task:
        return task
    
    entities = extract_entities_from_text(text)
    task.update(entities)
    
    return task

def infer_enhanced_task(text):
    """
    Enhanced task inference with entity extraction
    """
    base_task = infer_task_tool_from_text(text)
    if base_task:
        return enhance_task_with_entities(base_task, text)
    return base_task

# Alias for backward compatibility
def infer_task_from_text(text):
    return infer_task_tool_from_text(text)
