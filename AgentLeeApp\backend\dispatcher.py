"""
Agent <PERSON>er - Tool Registry and Execution System
Handles dynamic tool loading and execution based on tool_registry.json
"""
import json
import os
import importlib
import logging
from auto_task_generator import infer_task_tool_from_text

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger(__name__)

# Load tool registry from JSON
TOOL_REGISTRY_PATH = os.environ.get("TOOL_REGISTRY_PATH", os.path.join(os.path.dirname(__file__), "tool_registry.json"))

def load_tool_registry():
    """Load tool registry from JSON file"""
    try:
        with open(TOOL_REGISTRY_PATH, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load tool registry: {e}")
        return {}

# Global tool registry
TOOL_REGISTRY = load_tool_registry()

def dispatch(task, speaker):
    """
    Dispatch a task to the appropriate tool
    
    Args:
        task (dict): Task with 'tool' and 'input' keys
        speaker (str): Speaker identifier
        
    Returns:
        dict: Result from tool execution
    """
    if not task or not isinstance(task, dict):
        return {"error": "Invalid task format"}
    
    tool_name = task.get("tool")
    if not tool_name:
        return {"error": "No tool specified"}
    
    # Get tool info from registry
    tool_info = TOOL_REGISTRY.get(tool_name)
    if not tool_info:
        logger.warning(f"Tool not found: {tool_name}")
        return {"error": f"Tool not found: {tool_name}"}
    
    # Get module path
    module_path = tool_info.get("path")
    if not module_path:
        return {"error": f"No module path for tool: {tool_name}"}
    
    try:
        # Dynamic import of tool module
        module_parts = module_path.split('.')
        
        # Handle special case for MCP tools
        if module_parts[0] == "mcp":
            # For MCP tools, we'll use HTTP requests to the appropriate microservice
            return dispatch_to_mcp(tool_name, task, speaker)
        
        # For local modules
        module = importlib.import_module(module_path)
        
        # Look for run function
        if hasattr(module, 'run'):
            return module.run(task, speaker)
        else:
            logger.error(f"No run function in module: {module_path}")
            return {"error": f"Tool implementation error: {tool_name}"}
            
    except ImportError as e:
        logger.error(f"Failed to import tool module {module_path}: {e}")
        return {"error": f"Tool import error: {tool_name}"}
    except Exception as e:
        logger.exception(f"Error executing tool {tool_name}: {e}")
        return {"error": f"Tool execution error: {str(e)}"}

def dispatch_to_mcp(tool_name, task, speaker):
    """
    Dispatch a task to an MCP microservice
    
    Args:
        tool_name (str): Tool identifier
        task (dict): Task with 'input' and other parameters
        speaker (str): Speaker identifier
        
    Returns:
        dict: Result from MCP service
    """
    import requests
    import os
    import json
    
    # Load MCP settings
    mcp_settings_path = os.path.join(os.path.dirname(__file__), "mcphub", "mcp_settings.json")
    try:
        with open(mcp_settings_path, 'r') as f:
            mcp_settings = json.load(f)
    except Exception as e:
        logger.error(f"Failed to load MCP settings: {e}")
        return {"error": "MCP configuration error"}
    
    # Determine which MCP service to use
    mcp_servers = mcp_settings.get("mcpServers", {})
    
    # Default to mcphub for routing
    mcp_url = mcp_servers.get("mcphub", "http://mcphub:3000")
    
    # Prepare payload
    payload = {
        "tool": tool_name,
        "speaker": speaker,
        **task
    }
    
    # Send request to MCP service
    try:
        response = requests.post(
            f"{mcp_url}/api/tools/execute",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"MCP service error: {response.status_code} - {response.text}")
            return {"error": f"MCP service error: {response.status_code}"}
            
    except requests.RequestException as e:
        logger.error(f"Failed to connect to MCP service: {e}")
        return {"error": f"MCP connection error: {str(e)}"}

def run_tool(text, speaker):
    """
    Legacy function to infer and run a tool based on text input
    
    Args:
        text (str): User input text
        speaker (str): Speaker identifier
        
    Returns:
        dict: Result from tool execution
    """
    # Infer task from text
    task = infer_task_tool_from_text(text)
    
    # If no specific task was inferred, default to LLM chat
    if not task:
        task = {"tool": "llm_chat", "input": text}
    
    # Dispatch the task
    return dispatch(task, speaker)