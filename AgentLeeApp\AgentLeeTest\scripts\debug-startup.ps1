#!/usr/bin/env pwsh

Write-Host "🔍 Agent Lee <PERSON>bug Startup" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# Function to check if port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Function to wait for port to be available
function Wait-ForPort {
    param([int]$Port, [string]$ServiceName, [int]$TimeoutSeconds = 30)
    
    Write-Host "⏳ Waiting for $ServiceName on port $Port..." -ForegroundColor Yellow
    
    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
    while ((Get-Date) -lt $timeout) {
        if (Test-Port -Port $Port) {
            Write-Host "✅ $ServiceName is ready on port $Port" -ForegroundColor Green
            return $true
        }
        Start-Sleep -Seconds 1
    }
    
    Write-Host "❌ $ServiceName failed to start on port $Port within $TimeoutSeconds seconds" -ForegroundColor Red
    return $false
}

# Kill any existing processes
Write-Host "🛑 Stopping existing processes..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -like "*python*" -or $_.ProcessName -like "*livekit*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Wait for ports to be free
Start-Sleep -Seconds 2

# Step 1: Start LiveKit Server
Write-Host "`n🎥 Starting LiveKit Server..." -ForegroundColor Blue
$livekitJob = Start-Job -ScriptBlock {
    Set-Location "d:\LLE+DOCKER\AgentLeeApp\livekit"
    .\livekit-server.exe --dev --bind 0.0.0.0 --port 7880
}

if (-not (Wait-ForPort -Port 7880 -ServiceName "LiveKit Server")) {
    Write-Host "❌ LiveKit Server failed to start" -ForegroundColor Red
    exit 1
}

# Step 2: Start Backend Server
Write-Host "`n🔧 Starting Backend Server..." -ForegroundColor Blue
$backendJob = Start-Job -ScriptBlock {
    Set-Location "d:\LLE+DOCKER\AgentLeeApp\backend"
    $env:LIVEKIT_SERVER_URL = "ws://localhost:7880"
    $env:LIVEKIT_API_KEY = "devkey"
    $env:LIVEKIT_API_SECRET = "secret"
    $env:FLASK_ENV = "development"
    $env:PORT = "8000"
    d:\LLE+DOCKER\.venv\Scripts\python.exe server.py
}

if (-not (Wait-ForPort -Port 8000 -ServiceName "Backend Server")) {
    Write-Host "❌ Backend Server failed to start" -ForegroundColor Red
    exit 1
}

# Step 3: Test Backend Health
Write-Host "`n🏥 Testing Backend Health..." -ForegroundColor Blue
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/health" -TimeoutSec 10
    Write-Host "✅ Backend health check passed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Backend health check failed, but continuing..." -ForegroundColor Yellow
}

# Step 4: Test LiveKit Token Generation
Write-Host "`n🎫 Testing LiveKit Token Generation..." -ForegroundColor Blue
try {
    $tokenResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/livekit/token" -Method POST -ContentType "application/json" -Body '{"identity":"debug_user","room":"agent-lee-room"}' -TimeoutSec 10
    if ($tokenResponse.token) {
        Write-Host "✅ LiveKit token generation working" -ForegroundColor Green
    } else {
        Write-Host "⚠️ LiveKit token generation returned no token" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ LiveKit token generation failed: $_" -ForegroundColor Red
}

# Step 5: Start Frontend Server
Write-Host "`n🌐 Starting Frontend Server..." -ForegroundColor Blue
$frontendJob = Start-Job -ScriptBlock {
    Set-Location "d:\LLE+DOCKER\AgentLeeApp\frontend\agentlee3"
    python -m http.server 8080
}

if (-not (Wait-ForPort -Port 8080 -ServiceName "Frontend Server")) {
    Write-Host "❌ Frontend Server failed to start" -ForegroundColor Red
    exit 1
}

# Step 6: Test Frontend
Write-Host "`n🖥️ Testing Frontend..." -ForegroundColor Blue
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend is serving correctly" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend test failed: $_" -ForegroundColor Red
}

# Display final status
Write-Host "`n🎉 Debug Startup Complete!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host "🎥 LiveKit Server: http://localhost:7880" -ForegroundColor Cyan
Write-Host "🔧 Backend Server: http://localhost:8000" -ForegroundColor Cyan
Write-Host "🌐 Frontend Server: http://localhost:8080" -ForegroundColor Cyan

Write-Host "`n🔍 Debug Information:" -ForegroundColor Magenta
Write-Host "• LiveKit API Key: devkey" -ForegroundColor White
Write-Host "• LiveKit API Secret: secret" -ForegroundColor White
Write-Host "• LiveKit Server URL: ws://localhost:7880" -ForegroundColor White

Write-Host "`n🧪 Quick Tests:" -ForegroundColor Cyan
Write-Host "• Backend Health: http://localhost:8000/api/health" -ForegroundColor White
Write-Host "• LiveKit Token: POST http://localhost:8000/api/livekit/token" -ForegroundColor White
Write-Host "• Frontend App: http://localhost:8080" -ForegroundColor White

Write-Host "`n⏳ All services are running. Press Ctrl+C to stop." -ForegroundColor Yellow

# Keep script running
try {
    while ($true) {
        Start-Sleep -Seconds 5
        
        # Check if services are still running
        if (-not (Test-Port -Port 7880)) {
            Write-Host "❌ LiveKit Server stopped unexpectedly!" -ForegroundColor Red
        }
        if (-not (Test-Port -Port 8000)) {
            Write-Host "❌ Backend Server stopped unexpectedly!" -ForegroundColor Red
        }
        if (-not (Test-Port -Port 8080)) {
            Write-Host "❌ Frontend Server stopped unexpectedly!" -ForegroundColor Red
        }
    }
} finally {
    Write-Host "`n🛑 Stopping all services..." -ForegroundColor Yellow
    Get-Job | Stop-Job
    Get-Job | Remove-Job
    Get-Process | Where-Object {$_.ProcessName -like "*python*" -or $_.ProcessName -like "*livekit*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ All services stopped" -ForegroundColor Green
}