AGENT_LEE_ID=LEE_MCP_001
AGENT_LEE_ROLE=orchestrator
AGENTLEE_MCP_MODE=TRUE
AGENTLEE_MCP_VERSION=1.0.0
AGENTLEE_MCP_NAME=Agent Lee
DOCKER_USERNAME=4citeb4u
DOCKER_ACCESS_TOKEN=************************************
DOCKER_IMAGE_NAME=agentlee/mcp
DOCKER_IMAGE_TAG=latest
LANGCHAIN_API_KEY=ls__8572cbfc1c054fa08445b460bb69c974
LANGCHAIN_TRACING_V2=false
LANGCHAIN_PROJECT=CleanCoderAI
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
MCP_INDEXEDDB_NAMESPACE=agentlee_mcp
MCP_TASK_DB=agentlee_tasks
MCP_STATUS_DB=agentlee_status
MCP_LOG_DB=agentlee_logs
FIREBASE_API_KEY=AIzaSyDneLA9SPpbvlFMl9QkKhyhIqlbo-hZSHA
FIREBASE_DATABASE_URL=https://console.firebase.google.com/project/watson2024/firestore/databases/-default-/data/~2F
FIREBASE_STORAGE_BUCKET=watson2024.appspot.com
DUCKDUCKGO_SEARCH_API_KEY=23de90506707108ad5600e4949611d2a1e568cb8be470ba600517eb150e0ba67

# ===== Google Gemini Live (Primary Voice Pipeline) =====
# Agent Lee uses Google Gemini Live for voice conversations
GOOGLE_API_KEY=AIzaSyBpJiw1ym8tTPGI4Mi6a00qSPiudtgoDXo
# ===== Google Slides Integration =====
# For Google Slides MCP integration - using existing OAuth credentials
GOOGLE_CLIENT_ID=249742914141-d8hlmrc02pd7gmcgopev4gaiuhi8st3e.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
# Generate refresh token: https://developers.google.com/oauthplayground
GOOGLE_REFRESH_TOKEN=1//05GbfjueMQuc4CgYIARAAGAUSNwF-L9IrEp_cf24TAyhrW6BYl1KRXJ5565J9HqVJQzWhfQnRDK4oNfDpYT0miPfPl5C5DsJaUj0
# OpenRouter Integration
OPENROUTER_API_KEY=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
Model=google/gemma-3n-e2b-it:free
# ===== Email Configuration =====
# Primary email account for Agent Lee to send emails.
# IMPORTANT: Use an "App Password" for security, not your main password.
# Generate App Password: https://myaccount.google.com/apppasswords
EMAIL_USER=<EMAIL>
EMAIL_APP_PASSWORD=czmg xzpz qipz efjv
# ===== Telegram Configuration =====
# Token for your Telegram bot from BotFather.
TELEGRAM_BOT_TOKEN=**********************************************
# Your username (without @) to be recognized as an allowed user.
ALLOWED_USERS=Leonard
# Your personal chat ID for receiving notifications.
PERSONAL_TELEGRAM_CHAT_ID=**********
# ===== Personal Configuration =====
# Your personal email for Agent Lee to contact you.
PERSONAL_EMAIL=<EMAIL>
# Your personal phone number for Agent Lee to contact you.
LEONARD_PHONE_NUMBER=+***********
# Ngrok for public webhooks (if needed for LiveKit SIP)
NGROK_AUTH_TOKEN=*************************************************
agent_lee_public_url=https://4bc3-2603-6000-b400-5739-fd70-f099-5dba-8e7d.ngrok-free.app
Resend_API_KEY=re_evNnvdcV_HNBwVGvjqv9qSyq8Z68rEp44
Resend_API_URL=https://api.resend.com
Exa_API_KEY=b0b2bcd9-59fd-41cc-aad4-72cc353bd215
Exa_Team_ID=Team ID cmdgbry2j0022jhz5dx47qywj
ADMIN_EMAIL=<EMAIL>
EMAILJS_SERVICE_ID=service_wgaybsm
EMAILJS_TEMPLATE_ID=template_xxxx
EMAILJS_PUBLIC_KEY=X0xz2e39-ULZc-I47
EMAILJS_PRIVATE_KEY=-ZZ_QHFN4pgSLn-rDUCRs
4598b5c3663a2150536b261fd4584b1e