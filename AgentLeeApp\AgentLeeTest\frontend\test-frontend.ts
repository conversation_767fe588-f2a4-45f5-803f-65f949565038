/**
 * Comprehensive Frontend Test Suite for <PERSON> Lee
 * Tests all major functionality including authentication, LiveKit, voice recognition, and MCP integration
 */

// Type declarations
interface TestResult {
    timestamp: string;
    type: string;
    message: string;
    test: string;
}

interface TestUser {
    username: string;
    password: string;
}

declare global {
    interface Window {
        AgentLeeCore?: {
            backendURL: string;
        };
        SpeechRecognition?: new () => SpeechRecognition;
        webkitSpeechRecognition?: new () => SpeechRecognition;
        AgentLeeTestSuite?: typeof AgentLeeTestSuite;
    }
}

class AgentLeeTestSuite {
    private testResults: TestResult[];
    private backend_url: string;
    private testUser: TestUser;
    private currentTest: string | null;

    constructor() {
        this.testResults = [];
        this.backend_url = window.AgentLeeCore?.backendURL || 'http://localhost:5000';
        this.testUser = {
            username: 'test-user-' + Date.now(),
            password: 'test-password'
        };
        this.currentTest = null;
    }

    log(message: string, type: string = 'info'): void {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
        console.log(logMessage);
        
        // Add to results
        this.testResults.push({
            timestamp,
            type,
            message,
            test: this.currentTest || 'general'
        });
    }

    async runTest(testName: string, testFunction: () => Promise<void>): Promise<boolean> {
        this.currentTest = testName;
        this.log(`Starting test: ${testName}`, 'test');
        
        try {
            const startTime = Date.now();
            await testFunction();
            const duration = Date.now() - startTime;
            this.log(`✅ Test passed: ${testName} (${duration}ms)`, 'success');
            return true;
        } catch (error: any) {
            this.log(`❌ Test failed: ${testName} - ${error.message}`, 'error');
            console.error(error);
            return false;
        } finally {
            this.currentTest = null;
        }
    }

    // Test 1: Backend Connectivity
    async testBackendConnectivity(): Promise<void> {
        this.log('Testing backend connectivity...');
        
        const response = await fetch(`${this.backend_url}/health`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Backend health check failed with status: ${response.status}`);
        }

        const data = await response.json();
        this.log(`Backend health check passed: ${JSON.stringify(data)}`);
    }

    // Test 2: Authentication System
    async testAuthentication(): Promise<void> {
        this.log('Testing authentication system...');
        
        // Test user registration
        const registerResponse = await fetch(`${this.backend_url}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.testUser)
        });

        if (!registerResponse.ok) {
            throw new Error(`User registration failed with status: ${registerResponse.status}`);
        }

        this.log('User registration successful');

        // Test user login
        const loginResponse = await fetch(`${this.backend_url}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.testUser)
        });

        if (!loginResponse.ok) {
            throw new Error(`User login failed with status: ${loginResponse.status}`);
        }

        const loginData = await loginResponse.json();
        if (!loginData.token) {
            throw new Error('Login response missing token');
        }

        this.log('User authentication successful');
    }

    // Test 3: LiveKit Token Generation
    async testLiveKitToken(): Promise<void> {
        this.log('Testing LiveKit token generation...');
        
        const response = await fetch(`${this.backend_url}/livekit/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                identity: this.testUser.username,
                room: 'test-room'
            })
        });

        if (!response.ok) {
            throw new Error(`LiveKit token generation failed with status: ${response.status}`);
        }

        const data = await response.json();
        if (!data.token) {
            throw new Error('LiveKit token response missing token');
        }

        this.log('LiveKit token generation successful');
    }

    // Test 4: MCP Tool Dispatch
    async testMCPDispatch(): Promise<void> {
        this.log('Testing MCP tool dispatch...');
        
        const testTool = {
            name: 'test-tool',
            parameters: { message: 'Hello from test' }
        };

        const response = await fetch(`${this.backend_url}/mcp/dispatch`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testTool)
        });

        if (!response.ok) {
            throw new Error(`MCP dispatch failed with status: ${response.status}`);
        }

        const data = await response.json();
        this.log(`MCP dispatch successful: ${JSON.stringify(data)}`);
    }

    // Test 5: Frontend Components
    async testFrontendComponents(): Promise<void> {
        this.log('Testing frontend components...');

        // Check if React root exists
        const rootElement = document.getElementById('root');
        if (!rootElement) {
            throw new Error('React root element not found');
        }

        // Check if React app is mounted
        if (!rootElement.children.length) {
            throw new Error('React app not mounted');
        }

        this.log('React app mounted successfully');

        // Test CSS loading
        const computedStyle = window.getComputedStyle(document.body);
        if (!computedStyle.fontFamily.includes('Inter')) {
            this.log('Custom fonts may not be loaded', 'warning');
        }

        this.log('Frontend components loaded successfully');
    }

    // Test 6: Speech Recognition
    async testSpeechRecognition(): Promise<void> {
        this.log('Testing speech recognition...');

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (!SpeechRecognition) {
            throw new Error('Speech Recognition API not supported');
        }

        const recognition = new SpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;

        this.log('Speech recognition API available and configured');
    }

    // Test 7: Text-to-Speech
    async testTextToSpeech(): Promise<void> {
        this.log('Testing text-to-speech...');

        if (!('speechSynthesis' in window)) {
            throw new Error('Speech Synthesis API not supported');
        }

        const voices = speechSynthesis.getVoices();
        this.log(`Found ${voices.length} available voices`);

        // Test speech synthesis
        const utterance = new SpeechSynthesisUtterance('Test message');
        utterance.volume = 0; // Silent test
        speechSynthesis.speak(utterance);

        this.log('Text-to-speech functionality verified');
    }

    // Test 8: Media Access
    async testMediaAccess(): Promise<void> {
        this.log('Testing media access...');

        try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: true, 
                video: false 
            });
            
            // Stop the stream immediately
            stream.getTracks().forEach(track => track.stop());
            
            this.log('Microphone access granted');
        } catch (error: any) {
            this.log(`Media access test: ${error.message}`, 'warning');
            // Don't fail the test as this might be expected in some environments
        }
    }

    // Test 9: Memory System
    async testMemorySystem(): Promise<void> {
        this.log('Testing memory system...');

        // Test localStorage
        const testKey = 'agentlee-test-' + Date.now();
        const testValue = { test: true, timestamp: Date.now() };

        localStorage.setItem(testKey, JSON.stringify(testValue));
        const retrieved = JSON.parse(localStorage.getItem(testKey) || '{}');

        if (retrieved.test !== true) {
            throw new Error('localStorage test failed');
        }

        localStorage.removeItem(testKey);
        this.log('Memory system (localStorage) working correctly');
    }

    // Test 10: Error Handling
    async testErrorHandling(): Promise<void> {
        this.log('Testing error handling...');

        try {
            // Test invalid endpoint
            await fetch(`${this.backend_url}/invalid-endpoint`);
        } catch (error: any) {
            this.log('Network error handling working correctly');
        }

        // Test invalid JSON parsing
        try {
            JSON.parse('invalid json');
        } catch (error: any) {
            this.log('JSON error handling working correctly');
        }

        this.log('Error handling mechanisms verified');
    }

    // Main test runner
    async runAllTests(): Promise<void> {
        this.log('🚀 Starting Agent Lee Frontend Test Suite');
        this.log(`Backend URL: ${this.backend_url}`);
        
        const tests: Array<[string, () => Promise<void>]> = [
            ['Backend Connectivity', () => this.testBackendConnectivity()],
            ['Authentication System', () => this.testAuthentication()],
            ['LiveKit Token Generation', () => this.testLiveKitToken()],
            ['MCP Tool Dispatch', () => this.testMCPDispatch()],
            ['Frontend Components', () => this.testFrontendComponents()],
            ['Speech Recognition', () => this.testSpeechRecognition()],
            ['Text-to-Speech', () => this.testTextToSpeech()],
            ['Media Access', () => this.testMediaAccess()],
            ['Memory System', () => this.testMemorySystem()],
            ['Error Handling', () => this.testErrorHandling()]
        ];

        let passed = 0;
        let failed = 0;

        for (const [testName, testFunction] of tests) {
            const result = await this.runTest(testName, testFunction);
            if (result) {
                passed++;
            } else {
                failed++;
            }
        }

        this.log('\n' + '='.repeat(50));
        this.log('📊 TEST SUMMARY');
        this.log('='.repeat(50));
        this.log(`✅ Passed: ${passed}`);
        this.log(`❌ Failed: ${failed}`);
        this.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

        if (failed === 0) {
            this.log('🎉 All tests passed! Frontend is ready for production!', 'success');
        } else if (passed > failed) {
            this.log('⚠️ Most tests passed. Some issues need attention.', 'warning');
        } else {
            this.log('❌ Multiple test failures. System needs debugging.', 'error');
        }

        // Export results
        (window as any).testResults = this.testResults;
        this.log('Test results exported to window.testResults');
    }
}

// Auto-run tests when script loads (if not in production)
if (window.location.hostname === 'localhost' || window.location.search.includes('test=true')) {
    window.addEventListener('load', async () => {
        // Wait for AgentLeeCore to initialize
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const testSuite = new AgentLeeTestSuite();
        window.AgentLeeTestSuite = testSuite;
        
        console.log('🧪 Agent Lee Test Suite loaded. Run tests with: AgentLeeTestSuite.runAllTests()');
        
        // Auto-run if URL parameter is set
        if (window.location.search.includes('autotest=true')) {
            await testSuite.runAllTests();
        }
    });
}

// Export for manual testing
window.AgentLeeTestSuite = AgentLeeTestSuite;
