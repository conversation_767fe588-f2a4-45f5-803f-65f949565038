# Agent <PERSON>end Dockerfile
FROM python:3.10-slim

# Install system dependencies, including for TTS and phone control
RUN apt-get update && apt-get install -y \
    build-essential portaudio19-dev \
    ffmpeg libsndfile1 \
    android-tools-adb

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the backend application code
COPY . .

# Expose the port the app runs on
EXPOSE 5000

# Command to run the application using gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "server:app"]
