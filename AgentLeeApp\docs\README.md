# Agent Lee Documentation

This directory contains all documentation for the Agent Lee project.

## Quick Start
- [Main README](README.md) - Project overview and setup instructions
- [Docker Setup](README.docker.md) - Docker deployment guide
- [Production Deployment](deploy-production.ps1) - Production deployment script

## Technical Documentation
- [Technical Analysis](TECHNICAL_ANALYSIS.md) - Detailed technical analysis
- [Engineering Documentation](ENGINEERING_DOCUMENTATION.md) - Engineering details
- [System Fixes Summary](SYSTEM_FIXES_SUMMARY.md) - Recent fixes and improvements

## Setup and Configuration
- [Setup Complete Guide](SETUP_COMPLETE.md) - Complete setup instructions
- [Living Docs Guide](LIVING_DOCS_GUIDE.md) - Documentation maintenance guide
- [Admin Dashboard](ADMIN_DASHBOARD_README.md) - Admin interface documentation

## Integration and Features
- [Agent Lee Complete Integration](AGENT_LEE_COMPLETE_INTEGRATION.md) - Integration guide
- [UI/UX Enhancements](AGENT_LEE_UI_UX_ENHANCEMENTS.md) - UI improvement details
- [Action Plan](ACTION_PLAN.md) - Development roadmap

## Project Structure
`
AgentLeeApp/
├── backend/         # Python Flask backend
├── frontend/        # React TypeScript frontend
│   └── agentlee3/   # Main React application
└── livekit/         # LiveKit server executable

docs/                # All documentation (this folder)
scripts/             # Deployment and utility scripts
tests/               # All test files
`

## Development Workflow
1. Backend: Python Flask with MCP tools integration
2. Frontend: React TypeScript with LiveKit for real-time communication
3. Docker: Multi-service architecture with nginx reverse proxy
4. Testing: Comprehensive test suite in the tests/ directory
