
// ...existing code...
import { MCPServer } from 'model-context-protocol';
import axios from 'axios';
import fs from 'fs';

export async function startServer(port = 3004) {
  const server = new MCPServer({ port });

  server.registerTool('streamVideo', {
    title: 'Video Streamer',
    description: 'Stream video by URL or file path',
    parameters: {
      type: 'object',
      properties: {
        url: { type: 'string' },
        filePath: { type: 'string' }
      },
      required: ['url', 'filePath']
    },
    returns: {
      type: 'object',
      properties: {
        video_base64: { type: 'string' }
      }
    }
  }, async ({ url, filePath }) => {
    let buffer;
    if (filePath) {
      buffer = fs.readFileSync(filePath);
    } else {
      const response = await axios.get(url!, { responseType: 'arraybuffer' });
      buffer = Buffer.from(response.data);
    }
    return { video_base64: buffer.toString('base64') };
  });

  await server.listen();
  console.log(`Video Streamer MCP running on port ${port}`);
}
