from flask import jsonify
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("email_sender")

def validate_email(email):
    """Validate email address format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def sanitize_input(text):
    """Basic input sanitization"""
    if not isinstance(text, str):
        return ""
    # Remove potential injection characters
    dangerous_chars = ['<script', '</script', 'javascript:', 'data:', 'vbscript:']
    for char in dangerous_chars:
        text = text.replace(char, '')
    return text.strip()

def validate_task(task, speaker):
    """Validate input task and speaker"""
    if not isinstance(task, dict):
        raise ValueError("Task must be a dictionary")
    if not speaker or not isinstance(speaker, str):
        raise ValueError("Speaker must be a non-empty string")
    
    # Validate email fields
    recipient = task.get("to", "")
    if recipient and not validate_email(recipient):
        raise ValueError(f"Invalid recipient email: {recipient}")

def run(task, speaker):
    """
    Enhanced email sender with proper SMTP support, validation, and logging
    """
    try:
        validate_task(task, speaker)
        
        # Extract and sanitize email parameters from task
        input_text = sanitize_input(task.get("input", ""))
        recipient = sanitize_input(task.get("to", ""))
        subject = sanitize_input(task.get("subject", f"Message from Agent Lee - {speaker}"))
        body = sanitize_input(task.get("body", input_text))

        logger.info(f"Processing email request from {speaker}")

        if not recipient:
            # Try to extract email from input text
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, input_text)
            if emails:
                recipient = emails[0]
                logger.info(f"Extracted recipient from input: {recipient}")
            else:
                logger.warning("No recipient email address found")
                return jsonify({"error": "No recipient email address provided."})

        if not validate_email(recipient):
            logger.warning(f"Invalid email format: {recipient}")
            return jsonify({"error": f"Invalid email format: {recipient}"})

        if not body:
            logger.warning("No email content provided")
            return jsonify({"error": "No email content provided."})

        # Email configuration from environment
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        sender_email = os.getenv("SENDER_EMAIL", "<EMAIL>")
        sender_password = os.getenv("SENDER_PASSWORD", "")

        if not sender_password:
            logger.warning("No SMTP password configured, using fallback simulation")
            # Fallback to EmailJS simulation
            return jsonify({
                "status": "Email queued (simulated)",
                "to": recipient,
                "subject": subject,
                "method": "emailjs_fallback",
                "speaker": speaker,
                "tool": "email_sender"
            })

        # Validate SMTP configuration
        if not validate_email(sender_email):
            logger.error(f"Invalid sender email configuration: {sender_email}")
            return jsonify({"error": "Invalid sender email configuration"})

        # Create message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = recipient
        message["Subject"] = subject

        # Add body
        message.attach(MIMEText(body, "plain"))

        logger.info(f"Attempting to send email to {recipient}")

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        logger.info(f"Email sent successfully to {recipient}")

        return jsonify({
            "status": "Email sent successfully",
            "to": recipient,
            "subject": subject,
            "method": "smtp",
            "speaker": speaker,
            "tool": "email_sender"
        })

    except ValueError as ve:
        logger.warning(f"Validation error: {str(ve)}")
        return jsonify({
            "error": str(ve),
            "speaker": speaker,
            "tool": "email_sender"
        })
    except smtplib.SMTPAuthenticationError as auth_error:
        logger.error(f"SMTP authentication failed: {str(auth_error)}")
        return jsonify({
            "error": "Email authentication failed. Please check credentials.",
            "speaker": speaker,
            "tool": "email_sender"
        })
    except smtplib.SMTPException as smtp_error:
        logger.error(f"SMTP error: {str(smtp_error)}")
        return jsonify({
            "error": f"Email server error: {str(smtp_error)}",
            "speaker": speaker,
            "tool": "email_sender"
        })
    except Exception as e:
        logger.exception(f"Unexpected error sending email for {speaker}: {str(e)}")
        return jsonify({
            "error": f"Failed to send email: {str(e)}",
            "speaker": speaker,
            "tool": "email_sender"
        })

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    print(result.get_json().get("status", "Email processing failed"))

if __name__ == "__main__":
    import sys
    print("[DEPRECATED] This script is deprecated. Please use backend/mcp/communication_tools.py for unified email and calendar tools.")
    sys.exit(0)