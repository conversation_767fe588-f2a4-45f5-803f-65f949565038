import React, { useEffect, useRef, useState, useCallback } from 'react';
import './App.css';
import { useAgentLeeChat } from './hooks/useAgentLeeChat';
import { LiveKitController } from './components/LiveKitController';
import DocumentManager from './components/DocumentManager';

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends React.Component<React.PropsWithChildren<{}>, ErrorBoundaryState> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('❌ App Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
          <div className="text-center p-8">
            <h1 className="text-2xl font-bold text-red-400 mb-4">❌ Something went wrong</h1>
            <p className="text-gray-300 mb-4">Error: {this.state.error?.message}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
            >
              🔄 Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const AppContent: React.FC = () => {
  // Backend integration hooks
  const { messages, sendMessage, isSpeaking, isLoading, clearMessages } = useAgentLeeChat();
  const [searchInput, setSearchInput] = useState('');
  const [webResults, setWebResults] = useState<any[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [chatHistory, setChatHistory] = useState<any[]>(() => {
    // Load from localStorage
    try {
      const data = localStorage.getItem('agentlee_chat_history');
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  });

  // Save chat history on change
  useEffect(() => {
    localStorage.setItem('agentlee_chat_history', JSON.stringify(chatHistory));
  }, [chatHistory]);

  // Save new messages to chat history
  useEffect(() => {
    if (messages.length > 0) {
      setChatHistory((prev) => {
        const last = prev[prev.length - 1];
        if (last && last.timestamp === messages[messages.length - 1].timestamp) return prev;
        return [...prev, ...messages];
      });
    }
  }, [messages]);

  const handleSendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;
    try {
      await sendMessage(message);
      setSearchInput('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, [sendMessage]);

  // Render chat history sidebar
  const renderChatHistory = () => (
    <div className="bg-gray-800 p-4 rounded-xl w-72 h-full overflow-y-auto mr-6">
      <h2 className="text-lg font-bold mb-4 text-blue-400">Chat History</h2>
      <button className="mb-2 text-xs text-red-400 underline" onClick={() => { setChatHistory([]); clearMessages(); localStorage.removeItem('agentlee_chat_history'); }}>Clear All</button>
      {chatHistory.length === 0 ? (
        <p className="text-gray-400">No past chats yet.</p>
      ) : (
        chatHistory.slice(-100).reverse().map((msg, idx) => (
          <div key={idx} className={`mb-2 p-2 rounded ${msg.sender === 'user' ? 'bg-blue-900/40' : 'bg-purple-900/40'}`}>
            <div className="text-xs text-gray-400">{msg.sender} - {new Date(msg.timestamp).toLocaleString()}</div>
            <div className="text-sm text-white">{msg.text}</div>
          </div>
        ))
      )}
    </div>
  );

  return (
    <div className="bg-gray-900 text-white font-sans min-h-screen px-2 py-6 max-w-full flex flex-row">
      {/* Chat History Sidebar */}
      {renderChatHistory()}
      <div className="flex-1 max-w-7xl mx-auto">
        <h1 className="agent-title text-center mb-8">Agent Lee</h1>
        {/* LiveKitController for voice/video/speech */}
        <div className="mb-8">
          <LiveKitController onMessage={console.log} />
        </div>
        {/* Search Input */}
        <div className="mb-8">
          <div className="flex gap-4 max-w-4xl mx-auto">
            <input
              type="text"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage(searchInput)}
              placeholder="Ask Agent Lee anything..."
              className="flex-1 px-6 py-4 bg-gray-800 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => handleSendMessage(searchInput)}
              disabled={isLoading || !searchInput.trim()}
              className="px-6 py-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-xl font-semibold transition-colors"
            >
              {isLoading ? 'Sending...' : 'Send'}
            </button>
          </div>
        </div>
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Website Results */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🌐 Website Results</h3>
            <div className="space-y-4">
              {webResults.length === 0 ? (
                <div className="bg-gray-800 p-4 rounded-md card-glow transition text-gray-400">No web results yet.</div>
              ) : (
                webResults.map((result, idx) => (
                  <div key={idx} className="bg-gray-800 p-4 rounded-md card-glow transition">
                    {result.image && <img src={result.image} alt="Web Result" className="rounded-md mb-2" />}
                    <a href={result.url} target="_blank" rel="noopener noreferrer" className="block text-blue-400 font-semibold text-lg text-left">{result.title}</a>
                    <p className="text-gray-300 text-sm mt-1">{result.snippet}</p>
                  </div>
                ))
              )}
            </div>
          </div>
          {/* Document View */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex justify-between items-center">
              📄 Document View
            </h3>
            <div className="space-y-4">
              <DocumentManager onDocumentUpload={setSelectedDocument} />
              {selectedDocument && (
                <div className="bg-gray-800 p-4 rounded-md card-glow mt-4">
                  <h4 className="text-white font-semibold mb-2">{selectedDocument.name}</h4>
                  {selectedDocument.type.includes('pdf') ? (
                    <iframe src={selectedDocument.url} title="PDF Document" className="w-full h-96 rounded" />
                  ) : selectedDocument.type.includes('image') ? (
                    <img src={selectedDocument.url} alt={selectedDocument.name} className="w-full h-96 object-contain rounded" />
                  ) : (
                    <a href={selectedDocument.url} target="_blank" rel="noopener noreferrer" className="text-blue-400 underline">Open Document</a>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <AppContent />
    </ErrorBoundary>
  );
};

export default App;