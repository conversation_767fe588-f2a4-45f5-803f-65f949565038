# Agent Lee Backend

This directory contains the backend for the Agent Lee project, a powerful, voice-activated AI assistant. The backend is a Flask application that provides a RESTful API for the frontend, handling tasks, memory, and other services.

## Project Architecture

The Agent Lee project is designed with a decoupled architecture:

- **Frontend:** A static, single-page application deployed on GitHub Pages. It is responsible for the user interface and all client-side logic.
- **Backend:** A containerized Flask application that serves as the API for the frontend. It is designed to be deployed on Fly.io or any other container-based hosting service.

This separation of concerns allows for independent development, scaling, and deployment of the frontend and backend.

## API Endpoints

- `/api/task/parse` (POST): Parses user input and infers the appropriate task or tool.
- `/api/memory/search` (GET): Searches the user's memory.
- `/api/sessions` (GET): Lists active user sessions.
- `/api/speakers/<speaker>/stats` (GET): Retrieves statistics for a specific speaker.
- `/api/speakers/<speaker>/badges` (GET): Retrieves badges for a specific speaker.
- `/api/speakers/<speaker>/resume` (POST): Generates a resume in PDF format.
- `/api/speakers/<speaker>/certificate` (POST): Generates a certificate in PDF format.
- `/api/leaderboard` (GET): Retrieves the badge leaderboard.
- `/api/tools` (GET): Lists all available tools.
- `/health` (GET): A health check endpoint.

## Security

- **Authentication:** The backend uses a simple token-based authentication system. The API token must be provided in the `Authorization` header of all requests to protected endpoints.
- **CORS:** Cross-Origin Resource Sharing (CORS) is enabled globally to allow the frontend, hosted on a different domain, to communicate with the backend.
- **Secrets:** All secrets, including the API token, should be managed through environment variables and should never be hard-coded in the application.

## Getting Started

### Running Locally

1.  **Install Dependencies:**

    ```sh
    pip install -r requirements.txt
    ```

2.  **Run Tests:**

    ```sh
    python test_phases.py
    ```

3.  **Start the Server:**

    ```sh
    gunicorn --bind 0.0.0.0:5000 server:app
    ```

### Running with Docker

1.  **Build the Docker Image:**

    ```sh
    docker build -t agentlee-backend .
    ```

2.  **Run the Docker Container:**

    ```sh
    docker run -p 5000:5000 -e AGENT_LEE_API_TOKEN=your-secret-token agentlee-backend
    ```

## Deployment

The backend is designed to be deployed to Fly.io. For detailed instructions, please refer to the `docs/DEPLOYMENT.md` file.

## See Also

- [Testing Guide](docs/TESTING.md)
- [Deployment Guide](docs/DEPLOYMENT.md)
- [Security Best Practices](docs/SECURITY.md)
