from flask import jsonify
import os
import requests
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("weather")

def validate_location(location):
    """Validate location input"""
    if not location or not isinstance(location, str):
        return False
    # Check for reasonable location length and characters
    if len(location) > 100 or len(location) < 2:
        return False
    # Allow letters, spaces, commas, hyphens, and periods
    if not re.match(r'^[a-zA-Z\s,.-]+$', location):
        return False
    return True

def sanitize_input(text):
    """Basic input sanitization"""
    if not isinstance(text, str):
        return ""
    # Remove potential injection characters and limit length
    text = re.sub(r'[<>"\';]', '', text)
    return text.strip()[:200]  # Limit to 200 chars

def validate_task(task, speaker):
    """Validate input task and speaker"""
    if not isinstance(task, dict):
        raise ValueError("Task must be a dictionary")
    if not speaker or not isinstance(speaker, str):
        raise ValueError("Speaker must be a non-empty string")

def run(task, speaker):
    """
    Weather information tool using OpenWeather API with enhanced validation and logging
    """
    try:
        validate_task(task, speaker)
        
        input_text = sanitize_input(task.get("input", ""))
        location = sanitize_input(task.get("location", ""))
        
        logger.info(f"Weather request from {speaker} for location: {location or 'extracted from input'}")
        
        # Extract location from input text if not provided
        if not location and input_text:
            # Simple location extraction patterns
            location_patterns = [
                r'weather (?:in|for|at) ([A-Za-z\s,.-]+)',
                r'(?:in|for|at) ([A-Za-z\s,.-]+) weather',
                r'([A-Za-z\s,.-]+) weather'
            ]
            
            for pattern in location_patterns:
                match = re.search(pattern, input_text, re.IGNORECASE)
                if match:
                    location = match.group(1).strip()
                    logger.info(f"Extracted location from input: {location}")
                    break
        
        if not location:
            logger.warning("No location specified in request")
            return jsonify({
                "error": "No location specified. Try 'weather in New York' or 'Dallas weather'",
                "speaker": speaker,
                "tool": "weather"
            })
        
        if not validate_location(location):
            logger.warning(f"Invalid location format: {location}")
            return jsonify({
                "error": f"Invalid location format: {location}",
                "speaker": speaker,
                "tool": "weather"
            })
        
        # Get OpenWeather API key
        api_key = os.getenv("OPENWEATHER_API_KEY")
        if not api_key:
            logger.error("OpenWeather API key not configured")
            return jsonify({
                "error": "OpenWeather API key not configured",
                "fallback": f"I'd love to get the weather for {location}, but I need my API key set up first.",
                "speaker": speaker,
                "tool": "weather"
            })
        
        # Get weather data
        base_url = "http://api.openweathermap.org/data/2.5/weather"
        params = {
            "q": location,
            "appid": api_key,
            "units": "imperial"  # Fahrenheit
        }
        
        logger.debug(f"Making API request to OpenWeather for {location}")
        
        response = requests.get(base_url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            weather_info = {
                "location": data["name"],
                "country": data["sys"]["country"],
                "temperature": round(data["main"]["temp"]),
                "feels_like": round(data["main"]["feels_like"]),
                "description": data["weather"][0]["description"].title(),
                "humidity": data["main"]["humidity"],
                "wind_speed": data["wind"]["speed"],
                "pressure": data["main"]["pressure"]
            }
            
            logger.info(f"Weather data retrieved successfully for {weather_info['location']}")
            
            # Create a natural response
            response_text = f"Weather in {weather_info['location']}, {weather_info['country']}: "
            response_text += f"{weather_info['temperature']}°F, {weather_info['description']}. "
            response_text += f"Feels like {weather_info['feels_like']}°F. "
            response_text += f"Humidity: {weather_info['humidity']}%, Wind: {weather_info['wind_speed']} mph"
            
            return jsonify({
                "response": response_text,
                "weather_data": weather_info,
                "speaker": speaker,
                "tool": "weather"
            })
        
        elif response.status_code == 404:
            logger.warning(f"Location not found: {location}")
            return jsonify({
                "error": f"Location '{location}' not found. Try being more specific.",
                "speaker": speaker,
                "tool": "weather"
            })
        
        elif response.status_code == 401:
            logger.error("Invalid API key")
            return jsonify({
                "error": "Weather service authentication failed",
                "speaker": speaker,
                "tool": "weather"
            })
        
        else:
            logger.error(f"Weather API error: {response.status_code}")
            return jsonify({
                "error": f"Weather service error: {response.status_code}",
                "speaker": speaker,
                "tool": "weather"
            })
            
    except requests.exceptions.Timeout:
        logger.error("Weather API request timeout")
        return jsonify({
            "error": "Weather service timeout. Please try again.",
            "speaker": speaker,
            "tool": "weather"
        })
    except requests.exceptions.RequestException as req_error:
        logger.error(f"Weather API request error: {str(req_error)}")
        return jsonify({
            "error": f"Weather service connection error: {str(req_error)}",
            "speaker": speaker,
            "tool": "weather"
        })
    except ValueError as ve:
        logger.warning(f"Validation error: {str(ve)}")
        return jsonify({
            "error": str(ve),
            "speaker": speaker,
            "tool": "weather"
        })
    except Exception as e:
        logger.exception(f"Unexpected error in weather lookup for {speaker}: {str(e)}")
        return jsonify({
            "error": f"Weather lookup error: {str(e)}",
            "speaker": speaker,
            "tool": "weather"
        })

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    response_data = result.get_json()
    print(response_data.get("response", response_data.get("error", "Weather lookup failed")))

if __name__ == "__main__":
    main()