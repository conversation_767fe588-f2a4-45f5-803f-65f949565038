from flask import Flask, request, send_file
from TTS.api import TTS
import uuid

app = Flask(__name__)
tts = TTS(model_name="tts_models/multilingual/multi-dataset/xtts_v2", progress_bar=False, gpu=False)

@app.route("/api/tts", methods=["POST"])
def generate_tts():
    data = request.get_json()
    text = data.get("text", "<PERSON> is now speaking.")
    out_path = f"/tmp/{uuid.uuid4()}.wav"
    tts.tts_to_file(text=text, file_path=out_path)
    return send_file(out_path, mimetype="audio/wav")

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5002)
