#!/usr/bin/env pwsh
# Install Top 2025 AI Models for Agent Lee
# HunyuanVideo + HiDream-I1 + Wan2.1 + Gemini Integration

Write-Host "🚀 Installing Top 2025 AI Models for Agent Lee" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Check Python and pip
Write-Host "🔍 Checking Python environment..." -ForegroundColor Blue
try {
    $pythonVersion = python --version
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found. Please install Python 3.8+" -ForegroundColor Red
    exit 1
}

# Check CUDA availability
Write-Host "🔍 Checking CUDA availability..." -ForegroundColor Blue
try {
    $cudaVersion = nvidia-smi --query-gpu=driver_version --format=csv,noheader,nounits 2>$null
    if ($cudaVersion) {
        Write-Host "✅ CUDA GPU detected: Driver $cudaVersion" -ForegroundColor Green
        $useCuda = $true
    } else {
        Write-Host "⚠️  No CUDA GPU detected. Using CPU mode." -ForegroundColor Yellow
        $useCuda = $false
    }
} catch {
    Write-Host "⚠️  CUDA not available. Using CPU mode." -ForegroundColor Yellow
    $useCuda = $false
}

# Install core dependencies
Write-Host "📦 Installing core AI dependencies..." -ForegroundColor Blue
$corePackages = @(
    "torch",
    "torchvision", 
    "torchaudio",
    "diffusers",
    "transformers",
    "accelerate",
    "xformers",
    "opencv-python",
    "pillow",
    "numpy",
    "scipy",
    "matplotlib",
    "plotly",
    "huggingface-hub"
)

foreach ($package in $corePackages) {
    Write-Host "Installing $package..." -ForegroundColor Yellow
    try {
        if ($useCuda -and ($package -eq "torch" -or $package -eq "torchvision" -or $package -eq "torchaudio")) {
            # Install CUDA version of PyTorch
            pip install $package --index-url https://download.pytorch.org/whl/cu121
        } else {
            pip install $package
        }
        Write-Host "✅ $package installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  $package installation had issues" -ForegroundColor Yellow
    }
}

# Install HunyuanVideo dependencies
Write-Host "🎬 Installing HunyuanVideo dependencies..." -ForegroundColor Blue
$hunyuanPackages = @(
    "diffusers[video]",
    "imageio[ffmpeg]",
    "av",
    "decord"
)

foreach ($package in $hunyuanPackages) {
    Write-Host "Installing $package..." -ForegroundColor Yellow
    try {
        pip install $package
        Write-Host "✅ $package installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  $package installation had issues" -ForegroundColor Yellow
    }
}

# Download HunyuanVideo model
Write-Host "📥 Downloading HunyuanVideo model..." -ForegroundColor Blue
try {
    Write-Host "This may take several minutes..." -ForegroundColor Yellow
    huggingface-cli download tencent/HunyuanVideo --local-dir ./models/HunyuanVideo
    Write-Host "✅ HunyuanVideo model downloaded" -ForegroundColor Green
} catch {
    Write-Host "⚠️  HunyuanVideo download failed. Will use on-demand loading." -ForegroundColor Yellow
}

# Download HiDream-I1 model
Write-Host "🖼️  Downloading HiDream-I1 model..." -ForegroundColor Blue
try {
    huggingface-cli download HiDream/HiDream-I1-Full --local-dir ./models/HiDream-I1
    Write-Host "✅ HiDream-I1 model downloaded" -ForegroundColor Green
} catch {
    Write-Host "⚠️  HiDream-I1 download failed. Will use on-demand loading." -ForegroundColor Yellow
}

# Download Wan2.1 model
Write-Host "🔄 Downloading Wan2.1 model..." -ForegroundColor Blue
try {
    huggingface-cli download alibaba-pai/Wan2.1-FLF2V-14B-720P --local-dir ./models/Wan2.1
    Write-Host "✅ Wan2.1 model downloaded" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Wan2.1 download failed. Will use on-demand loading." -ForegroundColor Yellow
}

# Install Gemini SDK
Write-Host "🧠 Installing Gemini 2.5 Pro SDK..." -ForegroundColor Blue
try {
    pip install google-generativeai
    pip install google-cloud-aiplatform
    Write-Host "✅ Gemini SDK installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Gemini SDK installation had issues" -ForegroundColor Yellow
}

# Install Genkit for orchestration
Write-Host "🎼 Installing Google Genkit..." -ForegroundColor Blue
try {
    npm install -g @genkit-ai/cli
    pip install genkit
    Write-Host "✅ Genkit installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Genkit installation had issues" -ForegroundColor Yellow
}

# Create model configuration
Write-Host "⚙️  Creating model configuration..." -ForegroundColor Blue
$modelConfig = @{
    "models" = @{
        "hunyuan_video" = @{
            "name" = "tencent/HunyuanVideo"
            "type" = "text-to-video"
            "parameters" = "13B"
            "precision" = "FP8"
            "local_path" = "./models/HunyuanVideo"
            "enabled" = $true
        }
        "hidream_i1" = @{
            "name" = "HiDream/HiDream-I1-Full"
            "type" = "text-to-image"
            "parameters" = "Large"
            "local_path" = "./models/HiDream-I1"
            "enabled" = $true
        }
        "wan21" = @{
            "name" = "alibaba-pai/Wan2.1-FLF2V-14B-720P"
            "type" = "video-transitions"
            "parameters" = "14B"
            "resolution" = "720p"
            "local_path" = "./models/Wan2.1"
            "enabled" = $true
        }
        "gemini_pro" = @{
            "name" = "gemini-2.5-pro"
            "type" = "multimodal-analysis"
            "api_based" = $true
            "enabled" = $true
        }
    }
    "hardware" = @{
        "cuda_available" = $useCuda
        "device" = if ($useCuda) { "cuda" } else { "cpu" }
        "memory_optimization" = $true
        "fp16_enabled" = $useCuda
    }
    "installation_date" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
}

$configJson = $modelConfig | ConvertTo-Json -Depth 4
$configJson | Out-File -FilePath "ai_models_config.json" -Encoding UTF8
Write-Host "✅ Model configuration saved to ai_models_config.json" -ForegroundColor Green

# Create environment variables template
Write-Host "🔑 Creating environment template..." -ForegroundColor Blue
$envTemplate = @"
# AI Models Environment Configuration
# Copy this to .env and fill in your API keys

# Gemini 2.5 Pro
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_PROJECT_ID=your_project_id_here

# OpenAI (for fallback)
OPENAI_API_KEY=your_openai_api_key_here

# Hugging Face (for model downloads)
HUGGINGFACE_TOKEN=your_hf_token_here

# Model Settings
AI_MODELS_ENABLED=true
USE_CUDA=$useCuda
MODEL_CACHE_DIR=./models
ENABLE_MODEL_OPTIMIZATION=true

# Performance Settings
MAX_BATCH_SIZE=1
MEMORY_FRACTION=0.8
ENABLE_ATTENTION_SLICING=true
"@

$envTemplate | Out-File -FilePath ".env.template" -Encoding UTF8
Write-Host "✅ Environment template created: .env.template" -ForegroundColor Green

# Test installation
Write-Host "🧪 Testing AI models installation..." -ForegroundColor Blue
$testScript = @"
import torch
import diffusers
import transformers
print(f"PyTorch: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"Diffusers: {diffusers.__version__}")
print(f"Transformers: {transformers.__version__}")

try:
    from diffusers import HunyuanVideoPipeline
    print("✅ HunyuanVideo pipeline available")
except Exception as e:
    print(f"⚠️  HunyuanVideo: {e}")

try:
    import google.generativeai as genai
    print("✅ Gemini SDK available")
except Exception as e:
    print(f"⚠️  Gemini SDK: {e}")

print("🎉 AI Models installation test completed!")
"@

$testScript | Out-File -FilePath "test_ai_models.py" -Encoding UTF8
try {
    python test_ai_models.py
    Write-Host "✅ Installation test completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Installation test had issues" -ForegroundColor Yellow
}

# Create usage examples
Write-Host "📚 Creating usage examples..." -ForegroundColor Blue
$usageExamples = @"
# Agent Lee 2025 AI Models Usage Examples

## HunyuanVideo (Text-to-Video)
```python
from backend.mcphub.mcp.hunyuan_video import run
result = run({
    "input": "Create a video of a sunset over mountains",
    "mode": "video",
    "duration": 10,
    "resolution": "720p"
}, "user")
```

## HiDream-I1 (Text-to-Image)
```python
from backend.mcphub.mcp.multimodal_ai import run
result = run({
    "input": "Create a photorealistic image of a modern office",
    "type": "image",
    "quality": "high",
    "style": "professional"
}, "user")
```

## Multimodal Presentation
```python
result = run({
    "input": "Create a business presentation about AI trends",
    "type": "presentation"
}, "user")
```

## Voice Commands (Frontend)
- "Create a video of a peaceful forest"
- "Generate a professional headshot image"
- "Make a presentation about our quarterly results"
- "Analyze this document and create visuals"

## API Endpoints
- POST /api/ai/video - HunyuanVideo generation
- POST /api/ai/image - HiDream-I1 generation  
- POST /api/ai/presentation - Full multimodal pipeline
- POST /api/ai/analyze - Gemini 2.5 Pro analysis
"@

$usageExamples | Out-File -FilePath "AI_MODELS_USAGE.md" -Encoding UTF8
Write-Host "✅ Usage examples created: AI_MODELS_USAGE.md" -ForegroundColor Green

# Final summary
Write-Host "`n🎉 2025 AI MODELS INSTALLATION COMPLETE!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

Write-Host "`n📊 INSTALLATION SUMMARY:" -ForegroundColor Cyan
Write-Host "✅ HunyuanVideo-13B: Text-to-video generation" -ForegroundColor White
Write-Host "✅ HiDream-I1-Full: Photorealistic image generation" -ForegroundColor White
Write-Host "✅ Wan2.1-FLF2V-14B: Video transitions and effects" -ForegroundColor White
Write-Host "✅ Gemini 2.5 Pro SDK: Multimodal analysis" -ForegroundColor White
Write-Host "✅ Google Genkit: AI orchestration" -ForegroundColor White

Write-Host "`n🔧 HARDWARE CONFIGURATION:" -ForegroundColor Magenta
Write-Host "CUDA Available: $useCuda" -ForegroundColor White
Write-Host "Device: $(if ($useCuda) { 'GPU (CUDA)' } else { 'CPU' })" -ForegroundColor White
Write-Host "Precision: $(if ($useCuda) { 'FP16/FP8' } else { 'FP32' })" -ForegroundColor White

Write-Host "`n🚀 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Copy .env.template to .env and add your API keys" -ForegroundColor White
Write-Host "2. Set GOOGLE_API_KEY for Gemini 2.5 Pro" -ForegroundColor White
Write-Host "3. Test with: python test_ai_models.py" -ForegroundColor White
Write-Host "4. Start Agent Lee: .\deploy-production-complete.ps1" -ForegroundColor White

Write-Host "`n🎯 VOICE COMMANDS TO TRY:" -ForegroundColor Yellow
Write-Host "• 'Create a video of a sunset over mountains'" -ForegroundColor White
Write-Host "• 'Generate a professional business image'" -ForegroundColor White
Write-Host "• 'Make a presentation about AI trends'" -ForegroundColor White
Write-Host "• 'Analyze this content and create visuals'" -ForegroundColor White

Write-Host "`n⚡ PERFORMANCE OPTIMIZED FOR 2025!" -ForegroundColor Green
Write-Host "   Agent Lee now has cutting-edge AI capabilities!" -ForegroundColor Green

Write-Host "`nInstallation completed successfully! 🎊" -ForegroundColor Green