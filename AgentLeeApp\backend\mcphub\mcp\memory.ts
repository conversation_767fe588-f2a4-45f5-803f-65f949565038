  return `🧠 Memory received: ${input}\n(Memory simulation)`;
}
if (require.main === module) {
  const input = process.argv[2] || '';
  main(input).then(console.log);
}

import { MCPServer } from 'model-context-protocol';

export async function startServer(port = 3007) {
  const server = new MCPServer({ port });
  const store = new Map<string, any>();

  server.registerTool('getMemory', {
    title: 'Get Memory',
    parameters: { type: 'object', properties: { key: { type: 'string' } }, required: ['key'] },
    returns: { type: 'object', properties: { value: { type: 'string' } } }
  }, async ({ key }) => ({ value: store.get(key) ?? null }));

  server.registerTool('setMemory', {
    title: 'Set Memory',
    parameters: { type: 'object', properties: { key: { type: 'string' }, value: { type: 'string' } }, required: ['key', 'value'] },
    returns: { type: 'object', properties: { success: { type: 'boolean' } } }
  }, async ({ key, value }) => {
    store.set(key, value);
    return { success: true };
  });

  await server.listen();
  console.log(`Memory MCP server running on port ${port}`);
}
