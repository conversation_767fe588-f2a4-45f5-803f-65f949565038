# Base Python image
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Pre-download XTTS model inside the image
RUN python -c "from TTS.api import TTS; TTS(model_name='tts_models/multilingual/multi-dataset/xtts_v2')"

# Ensure temp folder exists
RUN mkdir -p /tmp

# Copy project files
COPY . .

# Start the TTS server
CMD ["python", "server.py"]
