version: '3.9'

services:
  agentlee-xtts:
    build:
      context: ./xtts
      dockerfile: Dockerfile
    container_name: xtts-server
    ports:
      - "5002:5002"
    restart: unless-stopped

  mcp-puppeteer:
    image: mcp/puppeteer
    container_name: puppeteer-server
    ports:
      - "5010:8080"
    restart: unless-stopped
    environment:
      DOCKER_CONTAINER: "true"

  mcp-ddg:
    image: mcp/duckduckgo
    container_name: ddg-server
    ports:
      - "5011:8080"
    restart: unless-stopped
