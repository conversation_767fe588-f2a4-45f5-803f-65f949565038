#!/usr/bin/env python3
"""
AgentLee Full Stack Integration Test
Comprehensive end-to-end testing of the entire application
"""

import requests
import json
import time
import sys
from datetime import datetime

class FullStackTester:
    def __init__(self):
        self.results = []
        self.start_time = time.time()
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        if passed:
            print(f"✅ {test_name}")
        else:
            print(f"❌ {test_name}")
            if details:
                print(f"   Details: {details}")

    def test_backend_health(self):
        """Test if backend is responding"""
        try:
            response = requests.get('http://localhost:5000/health', timeout=5)
            if response.status_code == 200:
                self.log_test("Backend Health Check", True)
                return True
            else:
                self.log_test("Backend Health Check", False, f"Status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Backend Health Check", False, str(e))
            return False

    def test_api_endpoint(self):
        """Test basic API functionality"""
        try:
            response = requests.post(
                'http://localhost:5000/api/task/parse',
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer changeme'
                },
                json={
                    'speaker': 'test_user',
                    'text': 'Hello, this is a test message'
                },
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_test("API Endpoint Test", True)
                return True
            else:
                self.log_test("API Endpoint Test", False, f"Status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("API Endpoint Test", False, str(e))
            return False

    def test_frontend_access(self):
        """Test if frontend is accessible"""
        try:
            response = requests.get('http://localhost:80', timeout=5)
            if response.status_code == 200:
                self.log_test("Frontend Access Test", True)
                return True
            else:
                self.log_test("Frontend Access Test", False, f"Status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Frontend Access Test", False, str(e))
            return False

    def test_mcp_services(self):
        """Test MCP services"""
        services = [
            ('mcphub', 3000),
            ('aura', 8000),
            ('agentlee', 8020),
            ('phone', 8010),
            ('chrome', 9222),
            ('mobile', 9010),
            ('playwright', 3001),
            ('chart', 3020),
            ('xtts', 8080)
        ]
        
        results = []
        for service_name, port in services:
            try:
                response = requests.get(f'http://localhost:{port}', timeout=3)
                if response.status_code in [200, 404, 405]:
                    self.log_test(f"{service_name} Service", True)
                    results.append(True)
                else:
                    self.log_test(f"{service_name} Service", False, f"Status: {response.status_code}")
                    results.append(False)
            except requests.exceptions.RequestException:
                self.log_test(f"{service_name} Service", False, "Service not responding")
                results.append(False)
        
        return any(results)  # At least one service should be working

    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        try:
            # Step 1: Send a message
            response = requests.post(
                'http://localhost:5000/api/task/parse',
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer changeme'
                },
                json={
                    'speaker': 'e2e_test_user',
                    'text': 'Test message for end-to-end workflow'
                },
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_test("End-to-End Message Processing", True)
                
                # Step 2: Check memory
                memory_response = requests.get(
                    'http://localhost:5000/api/memory/search?query=test',
                    headers={'Authorization': 'Bearer changeme'},
                    timeout=5
                )
                
                if memory_response.status_code in [200, 404]:
                    self.log_test("End-to-End Memory Integration", True)
                    return True
                else:
                    self.log_test("End-to-End Memory Integration", False, f"Memory status: {memory_response.status_code}")
                    return False
            else:
                self.log_test("End-to-End Message Processing", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("End-to-End Workflow", False, str(e))
            return False

    def test_performance_metrics(self):
        """Test application performance"""
        try:
            # Test response time
            start_time = time.time()
            response = requests.get('http://localhost:5000/health', timeout=5)
            response_time = time.time() - start_time
            
            if response_time < 2.0:
                self.log_test("Response Time Performance", True, f"{response_time:.2f}s")
            else:
                self.log_test("Response Time Performance", False, f"Too slow: {response_time:.2f}s")
            
            # Test concurrent requests
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                futures = [executor.submit(requests.get, 'http://localhost:5000/health', timeout=5) 
                          for _ in range(3)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
                
            if all(r.status_code == 200 for r in results):
                self.log_test("Concurrent Request Handling", True, "3 concurrent requests successful")
            else:
                self.log_test("Concurrent Request Handling", False, "Some concurrent requests failed")
                
        except Exception as e:
            self.log_test("Performance Testing", False, str(e))

    def test_error_handling(self):
        """Test error handling scenarios"""
        # Test invalid authentication
        try:
            response = requests.post(
                'http://localhost:5000/api/task/parse',
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer invalid_token'
                },
                json={'speaker': 'test', 'text': 'Hello'},
                timeout=5
            )
            if response.status_code == 401:
                self.log_test("Invalid Authentication Handling", True)
            else:
                self.log_test("Invalid Authentication Handling", False, f"Expected 401, got {response.status_code}")
        except Exception as e:
            self.log_test("Invalid Authentication Handling", False, str(e))

        # Test missing required fields
        try:
            response = requests.post(
                'http://localhost:5000/api/task/parse',
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer changeme'
                },
                json={},
                timeout=5
            )
            if response.status_code == 400:
                self.log_test("Missing Fields Handling", True)
            else:
                self.log_test("Missing Fields Handling", False, f"Expected 400, got {response.status_code}")
        except Exception as e:
            self.log_test("Missing Fields Handling", False, str(e))

    def run_all_tests(self):
        """Run all full stack tests"""
        print("🧪 AgentLee Full Stack Integration Tests")
        print("=" * 50)
        print(f"Starting tests at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # Run all test categories
        self.test_backend_health()
        self.test_api_endpoint()
        self.test_frontend_access()
        self.test_mcp_services()
        self.test_end_to_end_workflow()
        self.test_performance_metrics()
        self.test_error_handling()
        
        # Generate report
        self.generate_report()

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 50)
        print("📊 FULL STACK TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for r in self.results if r['passed'])
        total = len(self.results)
        percentage = (passed / total * 100) if total > 0 else 0
        
        print(f"Passed: {passed}/{total} ({percentage:.1f}%)")
        
        if passed == total:
            print("🎉 ALL FULL STACK TESTS PASSED!")
        elif passed > 0:
            print("⚠️  PARTIAL SUCCESS - Some tests failed")
        else:
            print("❌ ALL FULL STACK TESTS FAILED!")
        
        # Save detailed results
        self.save_results()
        
        print(f"\n⏱️  Total test time: {time.time() - self.start_time:.2f} seconds")
        print("📄 Detailed results saved to test_results/")

    def save_results(self):
        """Save detailed test results to file"""
        import os
        os.makedirs('../test_results', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'../test_results/full_stack_test_results_{timestamp}.json'
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'duration': time.time() - self.start_time,
                'results': self.results
            }, f, indent=2)
        
        print(f"📄 Results saved to: {filename}")

def main():
    """Main test runner"""
    tester = FullStackTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main() 