#!/bin/bash
# Generate browser-safe env.js from .env for <PERSON> frontend
set -e
ENV_JS_PATH="../assets/env.js"
echo "window.env = {" > $ENV_JS_PATH
grep -E '^(GOOGLE_API_KEY|EMAILJS_PUBLIC_KEY|FIREBASE_API_KEY|AGENT_LEE_ID|TELEGRAM_BOT_TOKEN|PERSONAL_TELEGRAM_CHAT_ID)=' ../.env | while IFS='=' read -r key value; do
  key=$(echo $key | tr -d '\r')
  value=$(echo $value | tr -d '\r')
  echo "  $key: '$value'," >> $ENV_JS_PATH
done
echo "};" >> $ENV_JS_PATH
