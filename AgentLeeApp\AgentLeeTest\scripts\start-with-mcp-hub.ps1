#!/usr/bin/env pwsh
# Start Agent Lee with MCP Hub for Full Production Testing

Write-Host "🚀 Starting Agent Lee with MCP Hub" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Set environment variables
$env:FLASK_ENV = "production"
$env:PORT = "8000"
$env:MCP_HUB_URL = "http://localhost:3000"

# Check if MCP Hub is running
Write-Host "🔍 Checking MCP Hub status..." -ForegroundColor Blue
try {
    $mcpResponse = Invoke-RestMethod -Uri "http://localhost:3000/health" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ MCP Hub is running" -ForegroundColor Green
} catch {
    Write-Host "⚠️  MCP Hub not detected at localhost:3000" -ForegroundColor Yellow
    Write-Host "   Starting mock MCP Hub for testing..." -ForegroundColor Yellow
    
    # Start a simple mock MCP Hub
    $mockHubJob = Start-Job -ScriptBlock {
        # Simple Python HTTP server that mimics MCP Hub responses
        $pythonScript = @"
import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

class MCPMockHandler(http.server.BaseHTTPRequestHandler):
    def do_POST(self):
        if self.path == '/api/tools/execute':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                tool = data.get('tool', 'unknown')
                
                # Mock responses for different tools
                if tool == 'weather':
                    response = {
                        'success': True,
                        'data': {
                            'location': data.get('location', 'Unknown'),
                            'temperature': '72°F',
                            'condition': 'Sunny',
                            'humidity': '45%',
                            'wind': '5 mph'
                        }
                    }
                elif tool == 'llm_response':
                    response = {
                        'success': True,
                        'data': {
                            'response': f"Hello! I received your message: '{data.get('input', '')}'. This is a mock response from the MCP Hub."
                        }
                    }
                elif tool == 'screenshot':
                    response = {
                        'success': True,
                        'data': {
                            'message': 'Screenshot captured successfully (mock)',
                            'filename': 'screenshot_mock.png'
                        }
                    }
                else:
                    response = {
                        'success': True,
                        'data': {
                            'message': f'Mock response for tool: {tool}',
                            'tool': tool,
                            'input': data.get('input', '')
                        }
                    }
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode())
                
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                error_response = {'success': False, 'error': str(e)}
                self.wfile.write(json.dumps(error_response).encode())
    
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'status': 'OK', 'service': 'Mock MCP Hub'}
            self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

PORT = 3000
with socketserver.TCPServer(("", PORT), MCPMockHandler) as httpd:
    print(f"Mock MCP Hub serving at port {PORT}")
    httpd.serve_forever()
"@
        
        # Write Python script to temp file and run it
        $tempScript = [System.IO.Path]::GetTempFileName() + ".py"
        $pythonScript | Out-File -FilePath $tempScript -Encoding UTF8
        python $tempScript
    }
    
    Start-Sleep -Seconds 2
    Write-Host "✅ Mock MCP Hub started on port 3000" -ForegroundColor Green
}

# Install backend dependencies
Write-Host "📦 Installing backend dependencies..." -ForegroundColor Blue
Set-Location "backend"
pip install Flask==2.3.3 flask-cors==4.0.0 PyJWT==2.8.0 requests==2.31.0 python-dotenv==1.0.0 | Out-Null
Write-Host "✅ Dependencies installed" -ForegroundColor Green

# Start backend
Write-Host "🔧 Starting backend server..." -ForegroundColor Blue
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD/backend
    $env:FLASK_ENV = "production"
    $env:PORT = "8000"
    $env:MCP_HUB_URL = "http://localhost:3000"
    python server.py
}

Start-Sleep -Seconds 3

# Test backend health
Write-Host "🏥 Testing backend health..." -ForegroundColor Blue
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8000/health" -TimeoutSec 10
    Write-Host "✅ Backend is healthy" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend health check failed" -ForegroundColor Red
    exit 1
}

# Test MCP integration
Write-Host "🧪 Testing MCP integration..." -ForegroundColor Blue
try {
    $weatherTest = Invoke-RestMethod -Uri "http://localhost:8000/mcp/weather" -Method POST -Body '{"location":"Dallas"}' -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ MCP integration working" -ForegroundColor Green
} catch {
    Write-Host "⚠️  MCP integration test failed, but continuing..." -ForegroundColor Yellow
}

# Start frontend
Write-Host "🌐 Starting frontend server..." -ForegroundColor Blue
Set-Location "../frontend/agentlee3"
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD/frontend/agentlee3
    python -m http.server 8080
}

Start-Sleep -Seconds 2

# Display success message
Write-Host "`n🎉 Agent Lee with MCP Hub is Running!" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host "📱 Frontend: http://localhost:8080" -ForegroundColor Cyan
Write-Host "📡 Backend: http://localhost:8000" -ForegroundColor Cyan
Write-Host "🔧 MCP Hub: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🧪 Auto Tests: http://localhost:8080?autotest=true" -ForegroundColor Cyan

Write-Host "`n🎯 Voice Commands to Test:" -ForegroundColor Magenta
Write-Host "  • 'What's the weather like in Dallas?'" -ForegroundColor White
Write-Host "  • 'Take a screenshot'" -ForegroundColor White
Write-Host "  • 'Hello Agent Lee, how are you?'" -ForegroundColor White

Write-Host "`n🚀 Ready for GitHub Pages Deployment!" -ForegroundColor Green
Write-Host "  Frontend configured for: https://yourusername.github.io/AgentLeeApp" -ForegroundColor White
Write-Host "  Backend should be deployed to: https://agentlee.fly.dev" -ForegroundColor White

Write-Host "`n⏳ Servers running. Press Ctrl+C to stop." -ForegroundColor Yellow

try {
    while ($true) {
        Start-Sleep -Seconds 5
        
        # Check job status
        if ($backendJob.State -ne "Running") {
            Write-Host "❌ Backend stopped" -ForegroundColor Red
            break
        }
        if ($frontendJob.State -ne "Running") {
            Write-Host "❌ Frontend stopped" -ForegroundColor Red
            break
        }
    }
} finally {
    Write-Host "`n🛑 Stopping all servers..." -ForegroundColor Yellow
    Stop-Job $backendJob -ErrorAction SilentlyContinue
    Stop-Job $frontendJob -ErrorAction SilentlyContinue
    if ($mockHubJob) { Stop-Job $mockHubJob -ErrorAction SilentlyContinue }
    Remove-Job * -ErrorAction SilentlyContinue
    Write-Host "✅ Cleanup complete" -ForegroundColor Green
}