  return `📋 Notion received: ${input}\n(Notion simulation)`;
}
if (require.main === module) {
  const input = process.argv[2] || '';
  main(input).then(console.log);
}

import { MCPServer } from 'model-context-protocol';
import { Client } from '@notionhq/client';

export async function startServer(port = 3009, notionToken: string) {
  const notion = new Client({ auth: notionToken });
  const server = new MCPServer({ port });

  server.registerTool('createPage', {
    title: 'Notion Create Page',
    parameters: {
      type: 'object',
      properties: {
        parentId: { type: 'string' },
        title: { type: 'string' }
      },
      required: ['parentId', 'title']
    },
    returns: {
      type: 'object',
      properties: {
        pageId: { type: 'string' }
      }
    }
  }, async ({ parentId, title }) => {
    const response = await notion.pages.create({
      parent: { database_id: parentId },
      properties: {
        title: {
          title: [
            { text: { content: title } }
          ]
        }
      }
    });
    return { pageId: response.id };
  });

  await server.listen();
  console.log(`Notion MCP server listening on port ${port}`);
}
