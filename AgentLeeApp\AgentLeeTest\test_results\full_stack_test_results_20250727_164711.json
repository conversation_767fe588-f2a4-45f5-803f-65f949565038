{"timestamp": "2025-07-27T16:47:11.985205", "duration": 65.01738667488098, "results": [{"name": "Backend Health Check", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A221EB370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:46:11.027865"}, {"name": "API Endpoint Test", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/task/parse (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A221EBC40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:46:15.096517"}, {"name": "Frontend Access Test", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A22254550>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:46:19.156047"}, {"name": "mcphub Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:23.225041"}, {"name": "aura Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:27.315929"}, {"name": "agentlee Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:31.416060"}, {"name": "phone Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:35.468035"}, {"name": "chrome Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:39.537343"}, {"name": "mobile Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:43.594429"}, {"name": "playwright <PERSON>", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:47.663049"}, {"name": "chart Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:51.699131"}, {"name": "xtts Service", "passed": false, "details": "Service not responding", "timestamp": "2025-07-27T16:46:55.771197"}, {"name": "End-to-End Workflow", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/task/parse (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A22254550>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:46:59.813307"}, {"name": "Performance Testing", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A22255ED0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:47:03.854047"}, {"name": "Invalid Authentication Handling", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/task/parse (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A221EAB00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:47:07.918544"}, {"name": "Missing <PERSON>", "passed": false, "details": "HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/task/parse (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022A221EB250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-27T16:47:11.982893"}]}