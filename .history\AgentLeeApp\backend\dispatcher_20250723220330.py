import json
import importlib
from flask import jsonify

# Load tool registry
with open('/app/tool_registry.json') as f:
    TOOL_REGISTRY = json.load(f)

# Track which tool each speaker is using
SPEAKER_TASKS = {}

def dispatch(task, speaker):
    """
    Enhanced dispatcher with multi-speaker support and proper error handling
    """
    tool_name = task.get("tool")
    if not tool_name:
        return jsonify({"error": "No tool specified."}), 400

    tool_info = TOOL_REGISTRY.get(tool_name)
    if not tool_info:
        return jsonify({"error": f"Tool '{tool_name}' not found in registry."}), 404

    try:
        # Track speaker's current tool usage
        SPEAKER_TASKS[speaker] = tool_name

        # Import and run the tool module
        module = importlib.import_module(tool_info["path"])
        return module.run(task, speaker)
    except Exception as e:
        return jsonify({"error": f"Failed to run tool '{tool_name}': {str(e)}"}), 500

def run_tool(input_text, speaker="unknown"):
    """
    Legacy compatibility function - converts old trigger-based system to new dispatch
    """
    # Try to determine tool from input text triggers
    for tool_name, tool_info in TOOL_REGISTRY.items():
        if 'trigger' in tool_info and tool_info['trigger'] in input_text.lower():
            task = {"tool": tool_name, "input": input_text}
            return dispatch(task, speaker)

    # Default to LLM chat if no specific tool matches
    task = {"tool": "llm_chat", "input": input_text}
    return dispatch(task, speaker)
