#!/usr/bin/env python3
"""
AgentLee Comprehensive Test Suite
Master test runner that executes all tests and provides detailed results
"""

import os
import sys
import time
import requests
import subprocess
import json
from datetime import datetime
from pathlib import Path

class TestRunner:
    def __init__(self):
        self.results = {
            'frontend': {'passed': 0, 'failed': 0, 'tests': []},
            'backend': {'passed': 0, 'failed': 0, 'tests': []},
            'integration': {'passed': 0, 'failed': 0, 'tests': []},
            'docker': {'passed': 0, 'failed': 0, 'tests': []}
        }
        self.start_time = time.time()
        
    def log_test(self, category, test_name, passed, details=""):
        """Log test result"""
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results[category]['tests'].append(result)
        if passed:
            self.results[category]['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results[category]['failed'] += 1
            print(f"❌ {test_name}")
            if details:
                print(f"   Details: {details}")

    def test_docker_services(self):
        """Test Docker services are running"""
        print("\n🐳 Testing Docker Services...")
        
        services = [
            ('nginx', 80, 'Frontend Web Server'),
            ('backend', 5000, 'Backend API'),
            ('mcphub', 3000, 'MCP Hub'),
            ('aura', 8000, 'Emotion AI'),
            ('agentlee', 8020, 'Agent Lee MCP'),
            ('phone', 8010, 'Phone Control'),
            ('chrome', 9222, 'Browser Control'),
            ('mobile', 9010, 'Mobile Automation'),
            ('playwright', 3001, 'Playwright'),
            ('chart', 3020, 'Chart Service'),
            ('xtts', 8080, 'Text-to-Speech')
        ]
        
        for service_name, port, description in services:
            try:
                response = requests.get(f'http://localhost:{port}', timeout=3)
                if response.status_code in [200, 404, 405]:
                    self.log_test('docker', f"{service_name} ({description})", True)
                else:
                    self.log_test('docker', f"{service_name} ({description})", False, 
                                f"Status: {response.status_code}")
            except requests.exceptions.RequestException as e:
                self.log_test('docker', f"{service_name} ({description})", False, str(e))

    def test_backend_api(self):
        """Test backend API endpoints"""
        print("\n🔧 Testing Backend API...")
        
        # Test health endpoint
        try:
            response = requests.get('http://localhost:5000/health', timeout=5)
            if response.status_code == 200:
                self.log_test('backend', 'Health Check', True)
            else:
                self.log_test('backend', 'Health Check', False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test('backend', 'Health Check', False, str(e))
        
        # Test API authentication
        try:
            response = requests.post(
                'http://localhost:5000/api/task/parse',
                headers={'Content-Type': 'application/json', 'Authorization': 'Bearer changeme'},
                json={'speaker': 'test', 'text': 'Hello'},
                timeout=10
            )
            if response.status_code == 200:
                self.log_test('backend', 'API Authentication', True)
            else:
                self.log_test('backend', 'API Authentication', False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test('backend', 'API Authentication', False, str(e))
        
        # Test memory search
        try:
            response = requests.get(
                'http://localhost:5000/api/memory/search?query=test',
                headers={'Authorization': 'Bearer changeme'},
                timeout=5
            )
            if response.status_code in [200, 404]:
                self.log_test('backend', 'Memory Search', True)
            else:
                self.log_test('backend', 'Memory Search', False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test('backend', 'Memory Search', False, str(e))

    def test_frontend_accessibility(self):
        """Test frontend accessibility and structure"""
        print("\n🎨 Testing Frontend...")
        
        # Test if frontend is accessible
        try:
            response = requests.get('http://localhost:80', timeout=5)
            if response.status_code == 200:
                self.log_test('frontend', 'Frontend Accessibility', True)
                
                # Check for key HTML elements
                html_content = response.text
                checks = [
                    ('HTML Structure', '<html' in html_content and '</html>' in html_content),
                    ('Chat Interface', 'chatMessages' in html_content),
                    ('Voice Button', 'voiceBtn' in html_content),
                    ('Message Input', 'messageInput' in html_content),
                    ('Send Button', 'sendBtn' in html_content),
                    ('Tools Panel', 'tool-btn' in html_content),
                    ('JavaScript', 'agentlee.js' in html_content),
                    ('CSS Styling', 'style.css' in html_content)
                ]
                
                for check_name, passed in checks:
                    self.log_test('frontend', f'HTML Element: {check_name}', passed)
                    
            else:
                self.log_test('frontend', 'Frontend Accessibility', False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test('frontend', 'Frontend Accessibility', False, str(e))

    def test_integration_workflows(self):
        """Test end-to-end workflows"""
        print("\n🔗 Testing Integration Workflows...")
        
        # Test complete message flow
        try:
            # Send a test message
            response = requests.post(
                'http://localhost:5000/api/task/parse',
                headers={'Content-Type': 'application/json', 'Authorization': 'Bearer changeme'},
                json={'speaker': 'integration_test', 'text': 'Test message for integration'},
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_test('integration', 'Message Processing Flow', True)
            else:
                self.log_test('integration', 'Message Processing Flow', False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test('integration', 'Message Processing Flow', False, str(e))
        
        # Test session management
        try:
            response = requests.get(
                'http://localhost:5000/api/sessions',
                headers={'Authorization': 'Bearer changeme'},
                timeout=5
            )
            if response.status_code in [200, 404]:
                self.log_test('integration', 'Session Management', True)
            else:
                self.log_test('integration', 'Session Management', False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test('integration', 'Session Management', False, str(e))

    def test_performance(self):
        """Test application performance"""
        print("\n⚡ Testing Performance...")
        
        # Test response time
        try:
            start_time = time.time()
            response = requests.get('http://localhost:5000/health', timeout=5)
            response_time = time.time() - start_time
            
            if response_time < 2.0:  # Should respond within 2 seconds
                self.log_test('integration', 'Response Time', True, f"{response_time:.2f}s")
            else:
                self.log_test('integration', 'Response Time', False, f"Too slow: {response_time:.2f}s")
        except Exception as e:
            self.log_test('integration', 'Response Time', False, str(e))
        
        # Test concurrent requests
        try:
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(requests.get, 'http://localhost:5000/health', timeout=5) 
                          for _ in range(5)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
                
            if all(r.status_code == 200 for r in results):
                self.log_test('integration', 'Concurrent Requests', True, "5 concurrent requests successful")
            else:
                self.log_test('integration', 'Concurrent Requests', False, "Some concurrent requests failed")
        except Exception as e:
            self.log_test('integration', 'Concurrent Requests', False, str(e))

    def run_all_tests(self):
        """Run all test categories"""
        print("🧪 AgentLee Comprehensive Test Suite")
        print("=" * 50)
        print(f"Starting tests at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # Run all test categories
        self.test_docker_services()
        self.test_backend_api()
        self.test_frontend_accessibility()
        self.test_integration_workflows()
        self.test_performance()
        
        # Generate report
        self.generate_report()

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        total_passed = 0
        total_failed = 0
        
        for category, results in self.results.items():
            passed = results['passed']
            failed = results['failed']
            total = passed + failed
            
            total_passed += passed
            total_failed += failed
            
            if total > 0:
                percentage = (passed / total) * 100
                status = "✅ PASS" if failed == 0 else "⚠️  PARTIAL" if passed > 0 else "❌ FAIL"
                print(f"{category.upper():12} {status:10} {passed}/{total} ({percentage:.1f}%)")
        
        print("-" * 50)
        overall_total = total_passed + total_failed
        overall_percentage = (total_passed / overall_total) * 100 if overall_total > 0 else 0
        
        if total_failed == 0:
            print(f"🎉 OVERALL: ✅ ALL TESTS PASSED ({total_passed}/{overall_total})")
        elif total_passed > 0:
            print(f"⚠️  OVERALL: PARTIAL SUCCESS ({total_passed}/{overall_total} - {overall_percentage:.1f}%)")
        else:
            print(f"❌ OVERALL: ALL TESTS FAILED ({total_passed}/{overall_total})")
        
        # Save detailed results
        self.save_results()
        
        print(f"\n⏱️  Total test time: {time.time() - self.start_time:.2f} seconds")
        print("📄 Detailed results saved to test_results/")

    def save_results(self):
        """Save detailed test results to file"""
        os.makedirs('test_results', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'test_results/test_results_{timestamp}.json'
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'duration': time.time() - self.start_time,
                'results': self.results
            }, f, indent=2)
        
        print(f"📄 Results saved to: {filename}")

def main():
    """Main test runner"""
    runner = TestRunner()
    runner.run_all_tests()

if __name__ == "__main__":
    main() 