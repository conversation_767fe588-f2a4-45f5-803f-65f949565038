# Agent <PERSON>er Setup

This guide explains how to containerize and run the entire Agent Lee backend using Docker.

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Git (to clone the repository)

## Quick Start

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/agent-lee.git
   cd agent-lee/AgentLeeApp
   ```

2. Run the setup script:
   - On Linux/Mac: `./setup-docker.sh`
   - On Windows: `setup-docker.bat`

3. Open your browser and navigate to:
   - Frontend: http://localhost:80
   - Backend API: http://localhost:5000

## Manual Setup

If you prefer to set up manually:

1. Create a `.env` file from the template:
   ```bash
   cp .env.sample .env
   ```

2. Edit the `.env` file with your API keys and settings.

3. Build and start the containers:
   ```bash
   docker-compose build
   docker-compose up -d
   ```

## Architecture

The containerized Agent Lee backend consists of the following services:

- **backend**: Main Flask API service (port 5000)
- **xtts**: Text-to-Speech service (port 8080)
- **mcphub**: MCP Hub service (port 3000)
- **aura**: Emotion AI service (port 8000)
- **phone**: Phone control service (port 8010)
- **chrome**: Chrome interaction service (port 9222)
- **mobile**: Mobile automation service (port 9010)
- **playwright**: Playwright automation service (port 3001)
- **chart**: Chart generation service (port 3020)
- **nginx**: Frontend serving and reverse proxy (port 80)

## Persistent Data

The following data is persisted across container restarts:

- Memory data: Stored in Docker volume `memory_data`
- XTTS models: Stored in Docker volume `xtts_models`

## Useful Commands

- View logs:
  ```bash
  docker-compose logs -f
  ```

- Stop all services:
  ```bash
  docker-compose down
  ```

- Restart a specific service:
  ```bash
  docker-compose restart backend
  ```

- Access a container's shell:
  ```bash
  docker-compose exec backend bash
  ```

## Troubleshooting

### Service not starting

Check the logs for the specific service:
```bash
docker-compose logs service_name
```

### API connection issues

Make sure all services are running:
```bash
docker-compose ps
```

### Volume permission issues

If you encounter permission issues with volumes, try:
```bash
sudo chown -R $(id -u):$(id -g) ./data
```

## Hardware Access

For services that require hardware access (like phone-mcp for ADB):

1. Uncomment the relevant sections in docker-compose.yml:
   ```yaml
   phone:
     # ...
     privileged: true
     devices:
       - /dev/bus/usb:/dev/bus/usb
   ```

2. Restart the service:
   ```bash
   docker-compose up -d phone
   ```

## Security Notes

- The default setup uses HTTP. For production, configure HTTPS.
- API keys are stored in the .env file. Keep this file secure.
- The nginx configuration includes basic security headers.