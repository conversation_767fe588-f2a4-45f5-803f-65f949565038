"""
document_tools.py - Unified document tools for <PERSON>rges summarize.py, resume_builder.py, and pdf_creator.py for summarization, resume, and PDF generation.
Each tool is exposed as a function: run_summarize, run_resume_builder, run_pdf_creator.
"""
from flask import jsonify
from datetime import datetime
import os

# --- Summarize ---
def run_summarize(task, speaker):
    text = task.get("input", "")
    if not text:
        return jsonify({"error": "No input text to summarize."})
    if len(text) <= 100:
        summary = text
        summary_type = "full_text"
    else:
        sentences = text.split('. ')
        if len(sentences) > 3:
            summary = '. '.join(sentences[:3]) + "..."
            summary_type = "truncated"
        else:
            summary = text[:200] + "..." if len(text) > 200 else text
            summary_type = "shortened"
    return jsonify({
        "summary": summary,
        "original_length": len(text),
        "summary_length": len(summary),
        "summary_type": summary_type,
        "speaker": speaker,
        "tool": "summarize"
    })

# --- Resume Builder ---
def run_resume_builder(task, speaker):
    try:
        input_text = task.get("input", "")
        resume_type = task.get("type", "standard")
        speaker_data = gather_speaker_data(speaker)
        if not input_text:
            return jsonify({"error": "No input provided for resume creation.", "speaker": speaker, "tool": "resume_builder"})
        if resume_type == "portfolio":
            return create_portfolio(speaker, speaker_data, input_text)
        elif resume_type == "skills":
            return create_skills_report(speaker, speaker_data, input_text)
        else:
            return create_standard_resume(speaker, speaker_data, input_text)
    except Exception as e:
        return jsonify({"error": f"Resume builder error: {str(e)}", "speaker": speaker, "tool": "resume_builder"})

def gather_speaker_data(speaker):
    data = {"badges": [], "skills": [], "projects": [], "interactions": 0, "tools_used": [], "achievements": {}}
    try:
        from mcp.badge_awarder import get_speaker_badges
        badge_data = get_speaker_badges(speaker)
        data["badges"] = badge_data.get("badges", [])
        data["achievements"] = badge_data.get("achievements", {})
        from memory_db import search_memory
        memories = search_memory(speaker)
        skills_keywords = {
            "Python": ["python", "flask", "django", "pandas"],
            "JavaScript": ["javascript", "js", "react", "node"],
            "Data Analysis": ["analyze", "data", "chart", "graph"],
            "Project Management": ["schedule", "calendar", "meeting", "project"],
            "Communication": ["email", "message", "presentation"],
            "Research": ["search", "research", "information", "study"],
            "Documentation": ["document", "pdf", "report", "summary"],
            "AI/ML": ["ai", "machine learning", "model", "algorithm"]
        }
        detected_skills = set()
        project_indicators = []
        for memory in memories:
            content = memory.get("content", "").lower()
            for skill, keywords in skills_keywords.items():
                if any(keyword in content for keyword in keywords):
                    detected_skills.add(skill)
            if any(word in content for word in ["create", "build", "develop", "design", "implement"]):
                project_indicators.append(memory.get("content", "")[:100])
        data["skills"] = list(detected_skills)
        data["projects"] = project_indicators[:5]
        data["interactions"] = len(memories)
        tool_keywords = {
            "Email Automation": ["email", "send message"],
            "Calendar Management": ["calendar", "schedule", "meeting"],
            "Document Creation": ["pdf", "document", "report"],
            "Data Analysis": ["analyze", "summary", "research"],
            "Weather Monitoring": ["weather", "forecast"],
            "Voice Processing": ["voice", "audio", "transcribe"]
        }
        tools_used = set()
        for memory in memories:
            content = memory.get("content", "").lower()
            for tool, keywords in tool_keywords.items():
                if any(keyword in content for keyword in keywords):
                    tools_used.add(tool)
        data["tools_used"] = list(tools_used)
    except Exception as e:
        print(f"Error gathering speaker data: {e}")
    return data

def create_standard_resume(speaker, data, additional_info):
    try:
        from mcp.pdf_creator import create_pdf_with_reportlab
        content = f"""
PROFESSIONAL SUMMARY
Experienced professional with demonstrated expertise in AI-assisted workflow automation and digital productivity tools. Proven track record of leveraging advanced AI systems for enhanced productivity and project management.

TECHNICAL SKILLS
{chr(10).join(f"• {skill}" for skill in data["skills"])}

AI TOOL PROFICIENCY
{chr(10).join(f"• {tool}" for tool in data["tools_used"])}

ACHIEVEMENTS & CERTIFICATIONS
{chr(10).join(f"• {badge}" for badge in data["badges"])}

PROFESSIONAL EXPERIENCE
AI Workflow Specialist
• Completed {data["interactions"]} successful AI-assisted tasks
• Demonstrated proficiency in {len(data["tools_used"])} different automation tools
• Achieved {len(data["badges"])} professional certifications through Agent Lee platform

PROJECT HIGHLIGHTS
{chr(10).join(f"• {project}" for project in data["projects"])}

ADDITIONAL INFORMATION
{additional_info}
        """.strip()
        task = {"type": "resume", "title": f"{speaker} - Professional Resume", "input": content}
        return create_pdf_with_reportlab(task, speaker, content, "resume", f"{speaker} - Professional Resume")
    except Exception as e:
        return jsonify({"error": f"Resume creation failed: {str(e)}", "speaker": speaker, "tool": "resume_builder"})

def create_portfolio(speaker, data, additional_info):
    content = f"""
{speaker.upper()} - PROFESSIONAL PORTFOLIO

EXECUTIVE SUMMARY
Dynamic professional with expertise in AI-driven productivity and automation. Demonstrated ability to leverage cutting-edge AI tools for enhanced workflow efficiency and project delivery.

CORE COMPETENCIES
Technical Skills: {", ".join(data["skills"])}
AI Tools Mastery: {", ".join(data["tools_used"])}
Total Interactions: {data["interactions"]} completed tasks
Achievement Level: {len(data["badges"])} certifications earned

PROFESSIONAL ACHIEVEMENTS
"""
    for badge, achievement in data["achievements"].items():
        content += f"\n🏅 {badge}\n   {achievement.get('description', '')}\n   Points: {achievement.get('points', 0)}\n"
    content += f"""

PROJECT PORTFOLIO
"""
    for i, project in enumerate(data["projects"], 1):
        content += f"\nProject {i}: {project}\n"
    content += f"""

ADDITIONAL INFORMATION
{additional_info}

GENERATED BY AGENT LEE AI ASSISTANT
This portfolio demonstrates proficiency in AI-assisted workflow automation and modern productivity tools.
"""
    task = {"type": "portfolio", "title": f"{speaker} - Professional Portfolio", "input": content}
    try:
        from mcp.pdf_creator import create_pdf_with_reportlab
        return create_pdf_with_reportlab(task, speaker, content, "portfolio", f"{speaker} - Professional Portfolio")
    except:
        return jsonify({"status": "Portfolio created (text format)", "content": content, "speaker": speaker, "tool": "resume_builder"})

def create_skills_report(speaker, data, additional_info):
    content = f"""
SKILLS ASSESSMENT REPORT - {speaker.upper()}

TECHNICAL PROFICIENCY ANALYSIS
Based on {data["interactions"]} AI-assisted interactions and task completions.

DEMONSTRATED SKILLS:
"""
    for skill in data["skills"]:
        content += f"• {skill} - Demonstrated through practical application\n"
    content += f"""

AI TOOL EXPERTISE:
"""
    for tool in data["tools_used"]:
        content += f"• {tool} - Active usage demonstrated\n"
    content += f"""

ACHIEVEMENT METRICS:
• Total Certifications: {len(data["badges"])}
• Total Points Earned: {sum(ach.get('points', 0) for ach in data["achievements"].values())}
• Task Completion Rate: 100% (based on successful interactions)
• Tool Diversity Score: {len(data["tools_used"])} / 10

CERTIFICATION DETAILS:
"""
    for badge in data["badges"]:
        content += f"• {badge}\n"
    content += f"""

RECOMMENDATIONS:
Based on demonstrated skills and achievements, {speaker} shows strong aptitude for:
- AI-assisted workflow automation
- Digital productivity optimization  
- Multi-tool integration and management
- Task completion and project delivery

{additional_info}
"""
    task = {"type": "skills", "title": f"{speaker} - Skills Assessment", "input": content}
    try:
        from mcp.pdf_creator import create_pdf_with_reportlab
        return create_pdf_with_reportlab(task, speaker, content, "skills", f"{speaker} - Skills Assessment")
    except:
        return jsonify({"status": "Skills report created (text format)", "content": content, "speaker": speaker, "tool": "resume_builder"})

# --- PDF Creator ---
def run_pdf_creator(task, speaker):
    try:
        input_text = task.get("input", "")
        pdf_type = task.get("type", "report")
        title = task.get("title", "Agent Lee Document")
        if not input_text and pdf_type == "report":
            return jsonify({"error": "No content provided for PDF creation."})
        try:
            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib.units import inch
            output_dir = "/app/data/pdfs"
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{speaker}_{pdf_type}_{timestamp}.pdf"
            filepath = os.path.join(output_dir, filename)
            doc = SimpleDocTemplate(filepath, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            story.append(Paragraph(title, styles['Title']))
            story.append(Spacer(1, 0.2*inch))
            story.append(Paragraph(f"<b>Generated for:</b> {speaker}", styles['Normal']))
            story.append(Paragraph(f"<b>Date:</b> {datetime.now().strftime('%B %d, %Y')}", styles['Normal']))
            story.append(Spacer(1, 0.3*inch))
            story.append(Paragraph("📄 DOCUMENT", styles['Heading1']))
            story.append(Spacer(1, 0.2*inch))
            paragraphs = input_text.split('\n')
            for para in paragraphs:
                if para.strip():
                    story.append(Paragraph(para, styles['Normal']))
                    story.append(Spacer(1, 0.1*inch))
            doc.build(story)
            return jsonify({"status": "PDF created successfully", "filename": filename, "filepath": filepath, "type": pdf_type, "title": title, "speaker": speaker, "tool": "pdf_creator"})
        except ImportError:
            return jsonify({"error": "PDF libraries not available. Install reportlab or fpdf2", "speaker": speaker, "tool": "pdf_creator"})
    except Exception as e:
        return jsonify({"error": f"PDF creation error: {str(e)}", "speaker": speaker, "tool": "pdf_creator"})

# Entrypoint for tool registry compatibility
def run(task, speaker):
    tool = task.get("tool")
    if tool == "summarize":
        return run_summarize(task, speaker)
    elif tool == "resume_builder":
        return run_resume_builder(task, speaker)
    elif tool == "pdf_creator":
        return run_pdf_creator(task, speaker)
    else:
        return jsonify({"error": f"Unknown document tool: {tool}"})
