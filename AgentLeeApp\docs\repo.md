---
description: Repository Information Overview
alwaysApply: true
---

# <PERSON> Information

## Summary
Agent <PERSON> is an AI assistant with a dynamic personality system that shifts between CEO, Hip-Hop, and Queen modes. It features voice recognition, text-to-speech, and integrates with various external services through a Model Context Protocol (MCP) architecture.

## Structure
- **AgentLeeApp/**: Main application with backend and frontend components
- **AgentLeeTest/**: Testing framework for the application
- **src/**: Shared TypeScript services and hooks
- **coqui_env/**: Python virtual environment for Coqui TTS
- **.venv/**: Main Python virtual environment

## Language & Runtime
**Backend Language**: Python 3.10+
**Frontend Language**: JavaScript/TypeScript with React
**Build System**: Docker Compose for backend, Node.js for frontend
**Package Manager**: pip (Python), npm (JavaScript)

## Dependencies

### Backend Dependencies
**Main Dependencies**:
- Flask 2.3.3 - Web framework
- TTS 0.22.0 - Coqui Text-to-Speech
- torch 2.2.2 - PyTorch for ML models
- livekit 0.10.1 - Real-time communication
- langchain-community 0.0.10 - Tool integration
- google-cloud-speech 2.21.0 - Speech recognition

**Development Dependencies**:
- pytest 7.4.0 - Testing framework

### Frontend Dependencies
**Main Dependencies**:
- react 18.2.0 - UI framework
- livekit-client 1.15.0 - Real-time communication
- @livekit/components-react 1.4.0 - LiveKit React components
- axios 1.6.0 - HTTP client

## Build & Installation
```bash
# Backend setup
cd AgentLeeApp
docker-compose up -d

# Frontend setup
cd AgentLeeApp/frontend/agentlee3
npm install
npm start
```

## Docker
**Docker Compose**: AgentLeeApp/docker-compose.yml
**Services**:
- backend: Main Flask API (port 5000)
- xtts: Text-to-Speech service (port 8080)
- mcphub: Model Context Protocol hub (port 3000)
- aura: Emotion AI service (port 8000)
- phone: Phone control service (port 8010)
- chrome: Chrome interaction service (port 9222)
- mobile: Mobile automation service (port 9010)
- playwright: Web automation service (port 3001)
- chart: Chart generation service (port 3020)
- nginx: Frontend serving and reverse proxy (port 80)

## Testing
**Framework**: pytest
**Test Location**: AgentLeeTest/ directory
**Structure**:
- backend/: Backend API tests
- frontend/: Frontend tests
- integration/: Integration tests including Docker and full-stack tests
**Run Command**:
```bash
cd AgentLeeTest
python run_all_tests.py
```

## MCP (Model Context Protocol) Tools
**Tool Registry**: AgentLeeApp/backend/tool_registry.json
**Available Tools**:
- puppeteer: Web automation
- desktop-commander: System automation
- duckduckgo: Web search
- github-official: GitHub integration
- google-maps: Location services
- openweather: Weather data
- playwright: Browser automation
- wikipedia: Knowledge base access
- youtube-transcripts: Video content analysis
- memory: Persistent conversation storage