# 🏅 Agent Lee — Badge Awarder & Certificate Notifier
import json
import os
from flask import jsonify
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("badge_awarder")

BADGES_FILE = "/app/data/badges.json"
ACHIEVEMENTS_FILE = "/app/data/achievements.json"

def load_badges():
    """Load badge data from JSON file"""
    if not os.path.exists(BADGES_FILE):
        logger.warning(f"Badges file not found: {BADGES_FILE}")
        return {}
    try:
        with open(BADGES_FILE, "r") as f:
            data = json.load(f)
            logger.info("Badges loaded successfully")
            return data
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.error(f"Error loading badges: {str(e)}")
        return {}

def save_badges(data):
    """Save badge data to JSON file"""
    try:
        os.makedirs(os.path.dirname(BADGES_FILE), exist_ok=True)
        with open(BADGES_FILE, "w") as f:
            json.dump(data, f, indent=2)
        logger.info("Badges saved successfully")
    except Exception as e:
        logger.error(f"Error saving badges: {str(e)}")

def load_achievements():
    """Load achievement definitions"""
    if not os.path.exists(ACHIEVEMENTS_FILE):
        logger.warning(f"Achievements file not found: {ACHIEVEMENTS_FILE}. Creating default.")
        # Create default achievements
        default_achievements = {
            "Getting Started": {
                "description": "Completed first interaction with Agent Lee",
                "icon": "🚀",
                "points": 10
            },
            "Module Completion": {
                "description": "Successfully completed a learning module",
                "icon": "📚",
                "points": 25
            },
            "Quiz Master": {
                "description": "Passed a quiz or assessment",
                "icon": "🧠",
                "points": 30
            },
            "Mentorship Badge": {
                "description": "Helped or mentored another user",
                "icon": "👥",
                "points": 50
            },
            "Productivity Pro": {
                "description": "Used 5+ different Agent Lee tools",
                "icon": "⚡",
                "points": 40
            },
            "Communication Champion": {
                "description": "Sent 10+ emails through Agent Lee",
                "icon": "📧",
                "points": 35
            },
            "Weather Watcher": {
                "description": "Checked weather 5+ times",
                "icon": "🌤️",
                "points": 15
            },
            "Calendar Keeper": {
                "description": "Created 5+ calendar events",
                "icon": "📅",
                "points": 20
            }
        }
        save_achievements(default_achievements)
        return default_achievements
    
    try:
        with open(ACHIEVEMENTS_FILE, "r") as f:
            data = json.load(f)
            logger.info("Achievements loaded successfully")
            return data
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.error(f"Error loading achievements: {str(e)}")
        return {}

def save_achievements(data):
    """Save achievement definitions"""
    try:
        os.makedirs(os.path.dirname(ACHIEVEMENTS_FILE), exist_ok=True)
        with open(ACHIEVEMENTS_FILE, "w") as f:
            json.dump(data, f, indent=2)
        logger.info("Achievements saved successfully")
    except Exception as e:
        logger.error(f"Error saving achievements: {str(e)}")

def validate_task(task, speaker):
    """Validate input task"""
    if not isinstance(task, dict):
        raise ValueError("Task must be a dictionary")
    if not speaker or not isinstance(speaker, str):
        raise ValueError("Speaker must be a non-empty string")
    # Additional validations can be added here

def run(task, speaker):
    """Main badge awarder function"""
    try:
        validate_task(task, speaker)
        input_text = task.get("input", "").lower()
        badge_name = task.get("badge") or detect_badge(input_text, speaker)

        if not badge_name:
            logger.info(f"No badge specified or detected for {speaker}")
            return jsonify({
                "error": "No badge specified or detected in input.",
                "speaker": speaker,
                "tool": "badge_awarder"
            })

        # Load current badges and achievements
        badges = load_badges()
        achievements = load_achievements()
        
        # Initialize speaker badges if not exists
        if speaker not in badges:
            badges[speaker] = {
                "badges": [],
                "total_points": 0,
                "achievements": {},
                "created_at": datetime.now().isoformat()
            }

        # Check if badge already awarded
        speaker_badges = badges[speaker]["badges"]
        if badge_name in speaker_badges:
            logger.info(f"Badge '{badge_name}' already awarded to {speaker}")
            return jsonify({
                "status": f"Badge '{badge_name}' already awarded",
                "speaker": speaker,
                "tool": "badge_awarder"
            })

        # Award the badge
        speaker_badges.append(badge_name)
        
        # Add points if achievement exists
        if badge_name in achievements:
            points = achievements[badge_name]["points"]
            badges[speaker]["total_points"] += points
            badges[speaker]["achievements"][badge_name] = {
                "awarded_at": datetime.now().isoformat(),
                "points": points,
                "description": achievements[badge_name]["description"]
            }
            logger.info(f"Awarded '{badge_name}' to {speaker} with {points} points")
        else:
            logger.warning(f"Achievement '{badge_name}' not found in definitions")

        # Save updated badges
        save_badges(badges)

        # Create response
        achievement_info = achievements.get(badge_name, {})
        icon = achievement_info.get("icon", "🏅")
        points = achievement_info.get("points", 0)
        description = achievement_info.get("description", "Achievement unlocked!")

        response_message = f"{icon} Congratulations {speaker}! You've earned the '{badge_name}' badge!"
        if points > 0:
            response_message += f" (+{points} points)"

        # Try to send email notification if email is provided
        email = task.get("email")
        if email:
            try:
                from mcp.email_sender import run as send_email
                email_task = {
                    "to": email,
                    "subject": f"🎓 New Badge Unlocked: {badge_name}",
                    "body": f"Dear {speaker},\n\n{response_message}\n\n{description}\n\nTotal Points: {badges[speaker]['total_points']}\n\n– Agent Lee",
                    "input": f"Badge notification for {badge_name}"
                }
                send_email(email_task, speaker)
                logger.info(f"Sent email notification to {email}")
            except Exception as e:
                logger.error(f"Failed to send badge email: {str(e)}")

        return jsonify({
            "status": "Badge awarded successfully",
            "badge": badge_name,
            "icon": icon,
            "points": points,
            "total_points": badges[speaker]["total_points"],
            "description": description,
            "message": response_message,
            "speaker": speaker,
            "tool": "badge_awarder"
        })

    except ValueError as ve:
        logger.warning(f"Validation error: {str(ve)}")
        return jsonify({
            "error": str(ve),
            "speaker": speaker,
            "tool": "badge_awarder"
        })
    except Exception as e:
        logger.exception(f"Badge award error for {speaker}: {str(e)}")
        return jsonify({
            "error": f"Badge award error: {str(e)}",
            "speaker": speaker,
            "tool": "badge_awarder"
        })

def detect_badge(text, speaker):
    """Detect which badge to award based on input text and speaker history"""
    
    # Load speaker's tool usage history
    badges = load_badges()
    speaker_data = badges.get(speaker, {"badges": [], "tool_usage": {}})
    
    # Basic keyword-based badge detection
    if any(keyword in text for keyword in ["complete", "done", "finished"]):
        if "quiz" in text or "test" in text:
            return "Quiz Master"
        elif "module" in text or "lesson" in text:
            return "Module Completion"
        else:
            return "Getting Started"
    
    elif any(keyword in text for keyword in ["help", "mentor", "assist", "teach"]):
        return "Mentorship Badge"
    
    elif "first time" in text or "new user" in text:
        return "Getting Started"
    
    # Check for productivity badges based on tool usage
    # This would be enhanced with actual usage tracking
    
    return None

def get_speaker_badges(speaker):
    """Get all badges for a specific speaker"""
    badges = load_badges()
    return badges.get(speaker, {"badges": [], "total_points": 0, "achievements": {}})

def get_leaderboard(limit=10):
    """Get top speakers by points"""
    badges = load_badges()
    leaderboard = []
    
    for speaker, data in badges.items():
        leaderboard.append({
            "speaker": speaker,
            "total_points": data.get("total_points", 0),
            "badge_count": len(data.get("badges", [])),
            "latest_badge": data.get("badges", [])[-1] if data.get("badges") else None
        })
    
    # Sort by points descending
    leaderboard.sort(key=lambda x: x["total_points"], reverse=True)
    return leaderboard[:limit]

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    response_data = result.get_json()
    print(response_data.get("message", response_data.get("status", "Badge processing failed")))

if __name__ == "__main__":
    import sys
    print("[DEPRECATED] This script is deprecated. Please use backend/mcp/document_tools.py for unified document tools.")
    sys.exit(0)
