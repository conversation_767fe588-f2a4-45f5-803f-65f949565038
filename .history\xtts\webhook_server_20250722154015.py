# Optional: GitHub webhook → XTTS voice trigger
from flask import Flask, request, jsonify
import requests
import os

app = Flask(__name__)

XTTS_API = os.environ.get("XTTS_API", "http://localhost:5002/api/tts")

@app.route("/webhook", methods=["POST"])
def github_webhook():
    data = request.json
    # Example: trigger voice on push event
    if data and data.get("ref"):
        text = f"GitHub push to {data['ref']} by {data['head_commit']['author']['name']}"
        payload = {"text": text, "language": "en"}
        r = requests.post(XTTS_API, json=payload)
        if r.ok:
            return jsonify({"status": "voice triggered"})
    return jsonify({"status": "ignored"})

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080)
