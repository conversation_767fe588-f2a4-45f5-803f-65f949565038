#!/usr/bin/env python3
"""
AgentLee Docker Integration Tests
Tests Docker containers, services, and full stack functionality
"""

import requests
import subprocess
import time
import json
import os
from datetime import datetime

class DockerTester:
    def __init__(self):
        self.results = []
        self.services = [
            ('nginx', 80, 'Frontend Web Server'),
            ('backend', 5000, 'Backend API'),
            ('mcphub', 3000, 'MCP Hub'),
            ('aura', 8000, 'Emotion AI'),
            ('agent<PERSON>', 8020, 'Agent <PERSON> MCP'),
            ('phone', 8010, 'Phone Control'),
            ('chrome', 9222, 'Browser Control'),
            ('mobile', 9010, 'Mobile Automation'),
            ('playwright', 3001, 'Playwright'),
            ('chart', 3020, 'Chart Service'),
            ('xtts', 8080, 'Text-to-Speech')
        ]
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        if passed:
            print(f"✅ {test_name}")
        else:
            print(f"❌ {test_name}")
            if details:
                print(f"   Details: {details}")

    def test_docker_installation(self):
        """Test if Docker is installed and running"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_test("Docker Installation", True, result.stdout.strip())
            else:
                self.log_test("Docker Installation", False, "Docker not found")
        except Exception as e:
            self.log_test("Docker Installation", False, str(e))

    def test_docker_compose(self):
        """Test if Docker Compose is available"""
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_test("Docker Compose", True, result.stdout.strip())
            else:
                self.log_test("Docker Compose", False, "Docker Compose not found")
        except Exception as e:
            self.log_test("Docker Compose", False, str(e))

    def test_container_status(self):
        """Test if all containers are running"""
        try:
            result = subprocess.run(['docker-compose', 'ps'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                output = result.stdout
                running_containers = [line for line in output.split('\n') 
                                    if 'Up' in line and 'agentlee' in line.lower()]
                
                if len(running_containers) >= 5:  # At least 5 core services
                    self.log_test("Container Status", True, f"{len(running_containers)} containers running")
                else:
                    self.log_test("Container Status", False, f"Only {len(running_containers)} containers running")
            else:
                self.log_test("Container Status", False, "Failed to check container status")
        except Exception as e:
            self.log_test("Container Status", False, str(e))

    def test_service_connectivity(self):
        """Test if all services are accessible"""
        print("\n🔗 Testing Service Connectivity...")
        
        for service_name, port, description in self.services:
            try:
                response = requests.get(f'http://localhost:{port}', timeout=3)
                if response.status_code in [200, 404, 405]:
                    self.log_test(f"{service_name} ({description})", True)
                else:
                    self.log_test(f"{service_name} ({description})", False, 
                                f"Status: {response.status_code}")
            except requests.exceptions.RequestException as e:
                self.log_test(f"{service_name} ({description})", False, str(e))

    def test_frontend_backend_integration(self):
        """Test frontend-backend integration"""
        print("\n🌐 Testing Frontend-Backend Integration...")
        
        # Test if frontend can reach backend through nginx proxy
        try:
            response = requests.get('http://localhost:80/api/health', timeout=5)
            if response.status_code in [200, 404]:
                self.log_test("Frontend-Backend Proxy", True)
            else:
                self.log_test("Frontend-Backend Proxy", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Frontend-Backend Proxy", False, str(e))

    def test_mcp_services_integration(self):
        """Test MCP services integration"""
        print("\n🔧 Testing MCP Services Integration...")
        
        # Test MCP Hub connectivity
        try:
            response = requests.get('http://localhost:3000', timeout=5)
            if response.status_code in [200, 404, 405]:
                self.log_test("MCP Hub Integration", True)
            else:
                self.log_test("MCP Hub Integration", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("MCP Hub Integration", False, str(e))

        # Test XTTS service
        try:
            response = requests.get('http://localhost:8080', timeout=5)
            if response.status_code in [200, 404, 405]:
                self.log_test("XTTS Service Integration", True)
            else:
                self.log_test("XTTS Service Integration", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("XTTS Service Integration", False, str(e))

    def test_volume_mounts(self):
        """Test Docker volume mounts"""
        try:
            # Check if data directory exists
            if os.path.exists('../AgentLeeApp/data'):
                self.log_test("Data Volume Mount", True)
            else:
                self.log_test("Data Volume Mount", False, "Data directory not found")
        except Exception as e:
            self.log_test("Data Volume Mount", False, str(e))

    def test_environment_variables(self):
        """Test environment variable configuration"""
        try:
            # Check if .env file exists
            if os.path.exists('../AgentLeeApp/.env'):
                self.log_test("Environment Configuration", True)
            else:
                self.log_test("Environment Configuration", False, ".env file not found")
        except Exception as e:
            self.log_test("Environment Configuration", False, str(e))

    def test_network_connectivity(self):
        """Test Docker network connectivity"""
        try:
            # Test if containers can communicate
            result = subprocess.run(['docker-compose', 'exec', '-T', 'backend', 'ping', '-c', '1', 'nginx'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_test("Container Network", True)
            else:
                self.log_test("Container Network", False, "Containers cannot communicate")
        except Exception as e:
            self.log_test("Container Network", False, str(e))

    def test_logs_accessibility(self):
        """Test if logs are accessible"""
        try:
            result = subprocess.run(['docker-compose', 'logs', '--tail=1'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                self.log_test("Logs Accessibility", True)
            else:
                self.log_test("Logs Accessibility", False, "No logs available")
        except Exception as e:
            self.log_test("Logs Accessibility", False, str(e))

    def test_resource_usage(self):
        """Test container resource usage"""
        try:
            result = subprocess.run(['docker', 'stats', '--no-stream', '--format', 'table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_test("Resource Monitoring", True)
            else:
                self.log_test("Resource Monitoring", False, "Cannot access resource stats")
        except Exception as e:
            self.log_test("Resource Monitoring", False, str(e))

    def run_all_tests(self):
        """Run all Docker tests"""
        print("🐳 AgentLee Docker Integration Tests")
        print("=" * 50)
        print(f"Starting tests at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        self.test_docker_installation()
        self.test_docker_compose()
        self.test_container_status()
        self.test_service_connectivity()
        self.test_frontend_backend_integration()
        self.test_mcp_services_integration()
        self.test_volume_mounts()
        self.test_environment_variables()
        self.test_network_connectivity()
        self.test_logs_accessibility()
        self.test_resource_usage()
        
        self.generate_summary()

    def generate_summary(self):
        """Generate test summary"""
        passed = sum(1 for r in self.results if r['passed'])
        total = len(self.results)
        percentage = (passed / total * 100) if total > 0 else 0
        
        print("\n" + "=" * 50)
        print("📊 DOCKER TEST SUMMARY")
        print("=" * 50)
        print(f"Passed: {passed}/{total} ({percentage:.1f}%)")
        
        if passed == total:
            print("🎉 ALL DOCKER TESTS PASSED!")
        elif passed > 0:
            print("⚠️  PARTIAL SUCCESS - Some tests failed")
        else:
            print("❌ ALL DOCKER TESTS FAILED!")
        
        # Save results
        self.save_results()

    def save_results(self):
        """Save test results"""
        os.makedirs('../test_results', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'../test_results/docker_test_results_{timestamp}.json'
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': self.results
            }, f, indent=2)
        
        print(f"📄 Results saved to: {filename}")

def main():
    """Main test runner"""
    tester = DockerTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main() 