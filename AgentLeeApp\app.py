#!/usr/bin/env python3
"""
Agent <PERSON> Application Server
Serves the React frontend and provides backend API endpoints
"""

import os
import sys
import logging
from pathlib import Path
from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, static_folder='build', static_url_path='')
CORS(app)

# Configuration
class Config:
    DEBUG = os.environ.get('FLASK_ENV') == 'development'
    SECRET_KEY = os.environ.get('SECRET_KEY', 'agent-lee-secret-key-change-in-production')
    STATIC_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'build')

app.config.from_object(Config)

@app.route('/')
def serve_react_app():
    """Serve the main React application"""
    try:
        return send_from_directory(app.static_folder, 'index.html')
    except Exception as e:
        logger.error(f"Error serving React app: {e}")
        return jsonify({'error': 'Application not found'}), 404

@app.route('/<path:path>')
def serve_static_files(path):
    """Serve static files (JS, CSS, images, etc.)"""
    try:
        return send_from_directory(app.static_folder, path)
    except Exception as e:
        logger.warning(f"Static file not found: {path}")
        # For React Router, return the main app
        return send_from_directory(app.static_folder, 'index.html')

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Agent Lee',
        'version': '1.0.0'
    })

@app.route('/api/agent/chat', methods=['POST'])
def agent_chat():
    """Agent Lee chat endpoint"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # TODO: Integrate with actual Agent Lee AI service
        response = {
            'response': f"Agent Lee received: {message}",
            'timestamp': '2025-07-30T22:00:00Z',
            'status': 'success'
        }
        
        return jsonify(response)
    
    except Exception as e:
        logger.error(f"Error in agent chat: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/agent/voice', methods=['POST'])
def agent_voice():
    """Agent Lee voice processing endpoint"""
    try:
        # TODO: Implement voice processing
        return jsonify({
            'message': 'Voice processing not yet implemented',
            'status': 'pending'
        })
    
    except Exception as e:
        logger.error(f"Error in voice processing: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors by serving the React app (for client-side routing)"""
    return send_from_directory(app.static_folder, 'index.html')

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

def check_static_files():
    """Check if static files exist"""
    static_path = Path(app.static_folder)
    index_path = static_path / 'index.html'
    
    if not static_path.exists():
        logger.error(f"Static folder not found: {static_path}")
        return False
    
    if not index_path.exists():
        logger.error(f"index.html not found: {index_path}")
        return False
    
    logger.info(f"Static files found at: {static_path}")
    return True

if __name__ == '__main__':
    logger.info("🚀 Starting Agent Lee Flask Server...")
    
    # Check if static files exist
    if not check_static_files():
        logger.error("❌ Static files not found. Please run 'npm run build' first.")
        sys.exit(1)
    
    # Get configuration from environment
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    logger.info(f"🌐 Server starting on http://{host}:{port}")
    logger.info(f"📁 Serving static files from: {app.static_folder}")
    logger.info(f"🔧 Debug mode: {debug}")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        logger.info("👋 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)
