#!/usr/bin/env python3
"""
Comprehensive Backend Test Suite for <PERSON> Lee
Tests all API endpoints, MCP tools, authentication, and system health
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

class AgentLeeBackendTestSuite:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.test_results = []
        self.auth_token = None
        self.test_user = {
            'username': f'test-user-{int(time.time())}',
            'password': 'test-password'
        }
        
    def log(self, message, test_type='info'):
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'type': test_type,
            'message': message,
            'test': getattr(self, 'current_test', 'general')
        }
        self.test_results.append(log_entry)
        
        color_codes = {
            'info': '\033[94m',      # Blue
            'success': '\033[92m',   # Green
            'warning': '\033[93m',   # Yellow
            'error': '\033[91m',     # Red
            'test': '\033[95m'       # Magenta
        }
        
        color = color_codes.get(test_type, '\033[0m')
        print(f"{color}[{timestamp}] {test_type.upper()}: {message}\033[0m")

    def run_test(self, test_name, test_function):
        self.current_test = test_name
        self.log(f"Starting test: {test_name}", 'test')
        
        try:
            start_time = time.time()
            test_function()
            duration = (time.time() - start_time) * 1000
            self.log(f"✅ Test passed: {test_name} ({duration:.1f}ms)", 'success')
            return True
        except Exception as e:
            self.log(f"❌ Test failed: {test_name} - {str(e)}", 'error')
            return False
        finally:
            self.current_test = None

    def test_health_check(self):
        """Test basic health endpoint"""
        response = requests.get(f"{self.base_url}/health", timeout=10)
        response.raise_for_status()
        
        data = response.json()
        if data.get('status') != 'OK':
            raise Exception(f"Health check failed: {data}")
        
        self.log("Backend health check passed")

    def test_authentication(self):
        """Test user authentication system"""
        # Test login
        login_data = {
            'name': self.test_user['username'],
            'password': self.test_user['password']
        }
        
        response = requests.post(
            f"{self.base_url}/api/login",
            json=login_data,
            timeout=10
        )
        response.raise_for_status()
        
        data = response.json()
        if not data.get('token'):
            raise Exception("No authentication token received")
        
        self.auth_token = data['token']
        self.log("Authentication successful")
        
        # Test authenticated endpoint
        headers = {'Authorization': f'Bearer {self.auth_token}'}
        response = requests.get(
            f"{self.base_url}/api/memory/search",
            headers=headers,
            timeout=10
        )
        response.raise_for_status()
        
        self.log("Authenticated endpoint access successful")

    def test_livekit_token(self):
        """Test LiveKit token generation"""
        if not self.auth_token:
            raise Exception("Authentication required for this test")
        
        headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
        token_data = {
            'identity': self.test_user['username'],
            'room': 'test-room'
        }
        
        response = requests.post(
            f"{self.base_url}/api/livekit/token",
            json=token_data,
            headers=headers,
            timeout=10
        )
        response.raise_for_status()
        
        data = response.json()
        if not data.get('token'):
            raise Exception("No LiveKit token received")
        
        self.log("LiveKit token generation successful")

    def test_mcp_tools(self):
        """Test MCP tool dispatch system"""
        # Test weather tool
        weather_data = {'location': 'Dallas'}
        response = requests.post(
            f"{self.base_url}/mcp/weather",
            json=weather_data,
            timeout=30
        )
        response.raise_for_status()
        
        self.log("Weather MCP tool responded successfully")
        
        # Test LLM tool
        llm_data = {'input': 'Hello, this is a test message for the LLM'}
        response = requests.post(
            f"{self.base_url}/mcp/llm_response",
            json=llm_data,
            timeout=30
        )
        response.raise_for_status()
        
        self.log("LLM MCP tool responded successfully")
        
        # Test tool registry
        response = requests.get(
            f"{self.base_url}/api/tools",
            headers={'Authorization': f'Bearer {self.auth_token}'},
            timeout=10
        )
        response.raise_for_status()
        
        data = response.json()
        if not data.get('tools'):
            raise Exception("No tools found in registry")
        
        self.log(f"Tool registry contains {len(data['tools'])} tools")

    def test_memory_system(self):
        """Test memory storage and retrieval"""
        if not self.auth_token:
            raise Exception("Authentication required for this test")
        
        headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
        # Test memory save
        test_message = f"Test memory message {int(time.time())}"
        memory_data = {
            'speaker': 'test-user',
            'text': test_message
        }
        
        response = requests.post(
            f"{self.base_url}/api/memory/save",
            json=memory_data,
            headers=headers,
            timeout=10
        )
        response.raise_for_status()
        
        # Test memory search
        response = requests.get(
            f"{self.base_url}/api/memory/search?query={test_message}",
            headers=headers,
            timeout=10
        )
        response.raise_for_status()
        
        self.log("Memory system working correctly")

    def test_task_processing(self):
        """Test task processing endpoint"""
        if not self.auth_token:
            raise Exception("Authentication required for this test")
        
        headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
        task_data = {
            'speaker': 'test-user',
            'text': 'What is the weather like today?',
            'tool': 'weather'
        }
        
        response = requests.post(
            f"{self.base_url}/api/task/parse",
            json=task_data,
            headers=headers,
            timeout=30
        )
        response.raise_for_status()
        
        self.log("Task processing endpoint working correctly")

    def test_error_handling(self):
        """Test error handling and edge cases"""
        # Test invalid endpoint
        response = requests.get(f"{self.base_url}/api/nonexistent")
        if response.status_code != 404:
            self.log(f"Expected 404 for invalid endpoint, got {response.status_code}", 'warning')
        
        # Test unauthorized access
        response = requests.get(f"{self.base_url}/api/memory/search")
        if response.status_code != 401:
            raise Exception(f"Expected 401 for unauthorized access, got {response.status_code}")
        
        # Test invalid JSON
        response = requests.post(
            f"{self.base_url}/api/login",
            data="invalid json",
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code not in [400, 422]:
            self.log(f"Expected 400/422 for invalid JSON, got {response.status_code}", 'warning')
        
        self.log("Error handling working correctly")

    def test_performance(self):
        """Test basic performance metrics"""
        # Test response times
        start_time = time.time()
        response = requests.get(f"{self.base_url}/health")
        health_time = (time.time() - start_time) * 1000
        
        if health_time > 1000:  # 1 second
            self.log(f"Health endpoint slow: {health_time:.1f}ms", 'warning')
        else:
            self.log(f"Health endpoint response time: {health_time:.1f}ms")
        
        # Test concurrent requests
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            try:
                start = time.time()
                response = requests.get(f"{self.base_url}/health", timeout=5)
                duration = (time.time() - start) * 1000
                results.put(('success', duration))
            except Exception as e:
                results.put(('error', str(e)))
        
        # Make 5 concurrent requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Collect results
        successes = 0
        total_time = 0
        while not results.empty():
            result_type, value = results.get()
            if result_type == 'success':
                successes += 1
                total_time += value
        
        if successes == 5:
            avg_time = total_time / successes
            self.log(f"Concurrent requests successful, avg time: {avg_time:.1f}ms")
        else:
            self.log(f"Only {successes}/5 concurrent requests succeeded", 'warning')

    def run_all_tests(self):
        """Run all tests and generate report"""
        self.log("🚀 Starting Agent Lee Backend Test Suite")
        self.log(f"Backend URL: {self.base_url}")
        
        tests = [
            ('Health Check', self.test_health_check),
            ('Authentication', self.test_authentication),
            ('LiveKit Token', self.test_livekit_token),
            ('MCP Tools', self.test_mcp_tools),
            ('Memory System', self.test_memory_system),
            ('Task Processing', self.test_task_processing),
            ('Error Handling', self.test_error_handling),
            ('Performance', self.test_performance)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_function in tests:
            if self.run_test(test_name, test_function):
                passed += 1
            else:
                failed += 1
            
            # Small delay between tests
            time.sleep(0.1)
        
        # Generate summary
        total = passed + failed
        success_rate = (passed / total * 100) if total > 0 else 0
        
        self.log("\n📊 Test Results Summary:")
        self.log(f"✅ Passed: {passed}")
        self.log(f"❌ Failed: {failed}")
        self.log(f"📈 Success Rate: {success_rate:.1f}%")
        
        # Save results to file
        report = {
            'timestamp': datetime.now().isoformat(),
            'backend_url': self.base_url,
            'summary': {
                'total': total,
                'passed': passed,
                'failed': failed,
                'success_rate': success_rate
            },
            'results': self.test_results
        }
        
        with open('backend_test_results.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        self.log("📄 Test report saved to backend_test_results.json")
        
        return {
            'passed': passed,
            'failed': failed,
            'total': total,
            'success_rate': success_rate
        }

def main():
    """Main test runner"""
    backend_url = os.environ.get('BACKEND_URL', 'http://localhost:5000')
    
    if len(sys.argv) > 1:
        backend_url = sys.argv[1]
    
    test_suite = AgentLeeBackendTestSuite(backend_url)
    
    try:
        results = test_suite.run_all_tests()
        
        # Exit with error code if tests failed
        if results['failed'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()