#!/bin/bash

# <PERSON> Docker Entrypoint Script
echo "🚀 Starting Agent Lee Application..."

# Set environment variables
export NODE_ENV=production
export PYTHONPATH=/app
export FLASK_APP=app.py
export FLASK_ENV=production

# Create log files
touch /app/logs/agent-lee.log
touch /app/logs/error.log

# Function to start services
start_services() {
    echo "📡 Starting Agent Lee services..."

    # Set Flask environment variables
    export FLASK_APP=app.py
    export FLASK_ENV=production
    export FLASK_HOST=0.0.0.0
    export FLASK_PORT=3000

    # Start main Flask application server (serves React + API)
    echo "🌶️ Starting Agent Lee Flask server on port 3000..."
    python app.py &
    MAIN_PID=$!

    # Start additional API server on port 8000 if needed
    echo "📡 Starting additional API server on port 8000..."
    cd /app
    python -c "
import http.server
import socketserver
import os

class APIHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{\"status\": \"healthy\", \"service\": \"Agent Lee API\"}')
        else:
            super().do_GET()

with socketserver.TCPServer(('', 8000), APIHandler) as httpd:
    print('API server running on port 8000')
    httpd.serve_forever()
" &
    API_PID=$!

    echo "✅ All services started successfully!"
    echo "🌐 Agent Lee is available at: http://localhost:3000"
    echo "📡 API server available at: http://localhost:8000"
}

# Function to handle shutdown
shutdown() {
    echo "🛑 Shutting down Agent Lee services..."

    if [ ! -z "$MAIN_PID" ]; then
        kill $MAIN_PID 2>/dev/null
    fi

    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null
    fi

    echo "👋 Agent Lee services stopped."
    exit 0
}

# Set up signal handlers
trap shutdown SIGTERM SIGINT

# Start services
start_services

# Keep the container running and monitor services
while true; do
    # Check if main service is still running
    if ! kill -0 $MAIN_PID 2>/dev/null; then
        echo "❌ Main service died, restarting..."
        cd /app/static
        python -m http.server 3000 &
        MAIN_PID=$!
    fi
    
    sleep 30
done
