<#
.SYNOPSIS
    A script to clean up obsolete and redundant files from the Agent Lee project.

.DESCRIPTION
    This script safely deletes a predefined list of old component files by searching
    recursively through all subdirectories to ensure they are found and removed,
    regardless of the project's folder structure.

.USAGE
    1. Save this code as `cleanup-recursive.ps1` in the root folder (`D:\LLE+DOCKER`).
    2. Open a new PowerShell terminal.
    3. Navigate to your project's root folder: `cd D:\LLE+DOCKER`.
    4. Run a "dry run" first to see what will be deleted:
       .\cleanup-recursive.ps1 -WhatIf
    5. To permanently delete the files, run:
       .\cleanup-recursive.ps1
#>
[CmdletBinding(SupportsShouldProcess=$true)]
param()

Write-Host "--- Starting Agent Lee Project Cleanup (Recursive Search) ---" -ForegroundColor Yellow

$filesToDelete = @(
    "App.js", "App_Old.js", "App_Original.js", "App_Enhanced.js", "App_Mobile.js",
    "LiveKitInterface.js", "LiveKitInterface_Original.js", "LiveKitInterface_Enhanced.js",
    "MockLiveKitInterface.js", "RealLiveKitInterface.js", "index_clean.js", "index_livekit.js",
    "index_production.js", "service-worker copy.js"
)

$filesDeleted = 0
$projectRoot = $PSScriptRoot # This will be D:\LLE+DOCKER when you run the script from there.

Write-Host "Searching for files to delete within '$projectRoot' and all its subfolders..."

foreach ($fileName in $filesToDelete) {
    # Recursively search for the file within the project root directory
    $foundFiles = Get-ChildItem -Path $projectRoot -Recurse -Filter $fileName -File -ErrorAction SilentlyContinue

    if ($foundFiles) {
        foreach ($file in $foundFiles) {
            if ($PSCmdlet.ShouldProcess($file.FullName, "Deleting obsolete file")) {
                Remove-Item -Path $file.FullName -Force
                if ($?) {
                    Write-Host "[DELETED] $($file.FullName)" -ForegroundColor Green
                    $filesDeleted++
                } else {
                    Write-Host "[ERROR] Could not delete $($file.FullName)" -ForegroundColor Red
                }
            }
        }
    } else {
        Write-Host "[SKIPPED] '$fileName' not found anywhere in the project." -ForegroundColor Gray
    }
}

Write-Host "------------------------------------------------------------" -ForegroundColor Yellow
Write-Host "Cleanup complete. Total files deleted: $filesDeleted" -ForegroundColor Cyan