from flask import jsonify
import os
import requests

def run(task, speaker):
    """
    LLM Chat tool - <PERSON>'s primary conversational interface
    """
    try:
        input_text = task.get("input", "")
        
        if not input_text:
            return jsonify({"error": "No input text provided for chat."})
        
        # Get OpenRouter API key
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            return jsonify({
                "response": f"Hey {speaker}! I hear you, but I need my OpenRouter API key to give you the full Agent Lee experience. For now, I'm just echoing back: {input_text}",
                "mode": "echo",
                "speaker": speaker,
                "tool": "llm_chat"
            })
        
        # Determine Agent <PERSON>'s personality mode based on time
        from datetime import datetime
        current_hour = datetime.now().hour
        
        if 6 <= current_hour < 12:
            mode = "ceo"
            system_prompt = "You are <PERSON> in CEO mode. Be professional, strategic, and execution-focused. Speak like a business leader who gets things done."
        elif 12 <= current_hour < 18:
            mode = "hiphop"
            system_prompt = "You are <PERSON> in Hip-Hop mode. Be authentic, creative, and culturally aware. Bring energy and keep it real while helping with tasks."
        else:
            mode = "queen"
            system_prompt = "You are <PERSON> in Queen mode. Be confident, empowering, and excellence-driven. Channel the energy of hip-hop queens and motivate users to elevate."
        
        # Prepare API request
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "anthropic/claude-3-sonnet",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Speaker {speaker} says: {input_text}"}
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }
        
        # Make API call
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result["choices"][0]["message"]["content"]
            
            return jsonify({
                "response": ai_response,
                "mode": mode,
                "speaker": speaker,
                "tool": "llm_chat",
                "model": "claude-3-sonnet"
            })
        else:
            return jsonify({
                "error": f"API error: {response.status_code}",
                "fallback_response": f"Yo {speaker}, I'm having some technical difficulties right now, but I'm still here for you. What's good?",
                "mode": mode,
                "speaker": speaker,
                "tool": "llm_chat"
            })
            
    except Exception as e:
        return jsonify({
            "error": f"Chat processing error: {str(e)}",
            "fallback_response": f"Hey {speaker}, I hit a snag but I'm still vibing with you. What's the move?",
            "speaker": speaker,
            "tool": "llm_chat"
        })

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    response_data = result.get_json()
    print(response_data.get("response", response_data.get("fallback_response", "No response")))

if __name__ == "__main__":
    main()
