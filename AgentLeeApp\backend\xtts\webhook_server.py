from flask import Flask, request, jsonify
import requests
import os

app = Flask(__name__)
XTTS_API = os.getenv("XTTS_API", "http://localhost:5002/api/tts")

@app.route("/webhook", methods=["POST"])
def handle_webhook():
    try:
        payload = request.json
        commit_msg = payload["head_commit"]["message"]

        response = requests.post(XTTS_API, json={
            "text": commit_msg,
            "speaker_wav": "https://example.com/sample.wav",  # replace with valid URL
            "language": "en"
        })

        if response.status_code == 200:
            return jsonify({"status": "TTS generated"}), 200
        else:
            return jsonify({"error": response.text}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080)


