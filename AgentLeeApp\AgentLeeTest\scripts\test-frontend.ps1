#!/usr/bin/env pwsh
# Quick Frontend Test Script

Write-Host "🧪 Testing Agent Lee Frontend" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

# Test if server is running
Write-Host "📡 Testing server connection..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081" -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Server is responding" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Server not responding: $_" -ForegroundColor Red
    exit 1
}

# Test JavaScript file MIME type
Write-Host "📄 Testing JavaScript MIME type..." -ForegroundColor Blue
try {
    $jsResponse = Invoke-WebRequest -Uri "http://localhost:8081/index.js" -TimeoutSec 5
    $contentType = $jsResponse.Headers.'Content-Type'
    if ($contentType -like "*javascript*") {
        Write-Host "✅ JavaScript served with correct MIME type: $contentType" -ForegroundColor Green
    } else {
        Write-Host "⚠️ JavaScript MIME type: $contentType" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to load JavaScript: $_" -ForegroundColor Red
}

# Test CSS file
Write-Host "🎨 Testing CSS file..." -ForegroundColor Blue
try {
    $cssResponse = Invoke-WebRequest -Uri "http://localhost:8081/style.css" -TimeoutSec 5
    if ($cssResponse.StatusCode -eq 200) {
        Write-Host "✅ CSS file loads successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ CSS file not found or error: $_" -ForegroundColor Yellow
}

# Test assets
Write-Host "🖼️ Testing assets..." -ForegroundColor Blue
try {
    $assetResponse = Invoke-WebRequest -Uri "http://localhost:8081/assets/agentlee_avatar.png" -TimeoutSec 5
    if ($assetResponse.StatusCode -eq 200) {
        Write-Host "✅ Assets load successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Assets not found or error: $_" -ForegroundColor Yellow
}

Write-Host "`n🎉 Frontend Test Complete!" -ForegroundColor Green
Write-Host "🌐 Open http://localhost:8081 in your browser" -ForegroundColor Cyan
Write-Host "🔧 Check browser console for any remaining errors" -ForegroundColor Yellow