# fly.toml file for Agent<PERSON>eeApp backend

app = "agentlee-backend"
primary_region = "iad"

[build]
  dockerfile = "AgentLeeApp/Dockerfile"

[env]
  PORT = "5000"
  AGENT_LEE_API_TOKEN = "your-secret-token" # Replace with a real secret

[[services]]
  internal_port = 5000
  protocol = "tcp"

  [[services.ports]]
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443
