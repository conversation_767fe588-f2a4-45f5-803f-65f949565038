import jwt
from flask import Flask, request, jsonify
from functools import wraps
from datetime import datetime, timedelta
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'supersecretkey'

# Token Validation
def validate_token(token):
    try:
        decoded = jwt.decode(token, app.config['SECRET_KEY'], algorithms=["HS256"])
        return decoded
    except jwt.ExpiredSignatureError:
        return False
    except jwt.InvalidTokenError:
        return False

# Auth Middleware
def auth_required(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        token = request.headers.get('Authorization', '').replace('Bearer ', '')
        if not validate_token(token):
            return jsonify({"error": "Unauthorized"}), 401
        return f(*args, **kwargs)
    return wrapper

@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    name = data.get('name')
    email = data.get('email')
    if name and email:
        token = jwt.encode({
            "user": name,
            "email": email,
            "exp": time.time() + 3600
        }, app.config['SECRET_KEY'], algorithm="HS256")
        return jsonify({"token": token})
    return jsonify({"error": "Missing credentials"}), 400

@app.route('/api/mcp/dispatch', methods=['POST'])
@auth_required
def dispatch_mcp():
    data = request.json
    tool = data.get('tool')
    payload = data.get('payload')
    if tool == "ping":
        target = payload.get("target", "8.8.8.8")
        return jsonify({"result": f"Pinging {target} successful"})
    return jsonify({"error": "Unknown tool"}), 400

@app.route('/api/memory/save', methods=['POST'])
@auth_required
def save_memory():
    data = request.json
    return jsonify({"status": "Saved", "data": data})

@app.route('/api/audit/summary', methods=['GET'])
@auth_required
def audit_summary():
    return jsonify({
        "system": "Agent Lee",
        "status": "Operational",
        "last_audit": time.strftime("%Y-%m-%d %H:%M:%S"),
        "verified": True
    })

if __name__ == '__main__':
    app.run(debug=True)
