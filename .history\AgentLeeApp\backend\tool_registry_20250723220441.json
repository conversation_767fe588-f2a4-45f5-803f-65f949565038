{"summarize": {"path": "mcp.summarize", "desc": "Summarize input text", "trigger": "summarize"}, "email_sender": {"path": "mcp.email_sender", "desc": "Send an email", "trigger": "send email"}, "calendar_event": {"path": "mcp.calendar_event", "desc": "Create calendar event", "trigger": "create calendar event"}, "sms_sender": {"path": "mcp.sms_sender", "desc": "Send Telegram message", "trigger": "send message"}, "file_tools": {"path": "mcp.file_tools", "desc": "File upload/download operations", "trigger": "file"}, "camera_tools": {"path": "mcp.camera_tools", "desc": "Camera functions and image capture", "trigger": "camera"}, "ai_toolkit": {"path": "mcp.ai_toolkit", "desc": "AI-driven utilities and analysis", "trigger": "analyze"}, "youtube_meta": {"path": "mcp.youtube_meta", "desc": "Fetch YouTube metadata and transcripts", "trigger": "youtube"}, "weather": {"path": "mcp.weather", "desc": "Current weather information", "trigger": "weather"}, "image_gen": {"path": "mcp.image_gen", "desc": "Generate images using AI", "trigger": "generate image"}, "pdf_creator": {"path": "mcp.pdf_creator", "desc": "Generate PDF reports and documents", "trigger": "create pdf"}, "file_backup": {"path": "mcp.file_backup", "desc": "Backup important files", "trigger": "backup"}, "llm_response": {"path": "mcp.llm_response", "desc": "Ask the language model", "trigger": "ask"}, "llm_tools": {"path": "mcp.llm_tools", "desc": "Extended LLM-powered tools", "trigger": "llm"}, "doc_analyzer": {"path": "mcp.doc_analyzer", "desc": "Analyze uploaded documents", "trigger": "analyze document"}, "voice_embedder": {"path": "mcp.voice_embedder", "desc": "Embed and analyze voice data", "trigger": "voice"}, "note_taker": {"path": "mcp.note_taker", "desc": "Voice-to-text transcription", "trigger": "take notes"}, "face_tracker": {"path": "mcp.face_tracker", "desc": "Track face and expression analysis", "trigger": "face"}, "sensor_bridge": {"path": "mcp.sensor_bridge", "desc": "Stream IoT sensor data", "trigger": "sensor"}, "llm_chat": {"path": "mcp.llm_chat", "desc": "Interactive AI chat responses", "trigger": "chat"}, "llm_com": {"path": "mcp.llm_com", "desc": "LLM communication orchestrator", "trigger": "communicate"}}