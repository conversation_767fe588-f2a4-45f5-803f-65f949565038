
// ...existing code...
import { MCPServer } from 'model-context-protocol';
import TelegramBot from 'node-telegram-bot-api';

export async function startServer(port = 3003, telegramToken: string) {
  const bot = new TelegramBot(telegramToken, { polling: false });
  const server = new MCPServer({ port });
  
  server.registerTool('sendMessage', {
    title: 'Telegram Send Message',
    description: 'Send a text message via Telegram Bot API',
    parameters: {
      type: 'object',
      properties: {
        chatId: { type: 'integer' },
        text: { type: 'string' }
      },
      required: ['chatId', 'text']
    },
    returns: {
      type: 'object',
      properties: {
        messageId: { type: 'integer' }
      }
    }
  }, async ({ chatId, text }) => {
    const msg = await bot.sendMessage(chatId, text);
    return { messageId: msg.message_id };
  });

  server.registerTool('sendPhoto', {
    title: 'Telegram Send Photo',
    description: 'Send a photo with optional caption',
    parameters: {
      type: 'object',
      properties: {
        chatId: { type: 'integer' },
        photoUrl: { type: 'string' },
        caption: { type: 'string' }
      },
      required: ['chatId', 'photoUrl']
    },
    returns: {
      type: 'object',
      properties: {
        messageId: { type: 'integer' }
      }
    }
  }, async ({ chatId, photoUrl, caption }) => {
    const msg = await bot.sendPhoto(chatId, photoUrl, { caption });
    return { messageId: msg.message_id };
  });

  await server.listen();
  console.log(`Telegram MCP server listening on port ${port}`);
}
