<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent Lee - Documentation</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white min-h-screen">
  <div class="max-w-3xl mx-auto p-8">
    <h1 class="text-4xl font-bold gradient-text mb-6">Agent <PERSON> Documentation</h1>
    <section class="mb-8">
      <h2 class="text-2xl font-semibold mb-2">Overview</h2>
      <p><PERSON> is a full-stack AI assistant with voice, vision, and workflow automation. The frontend runs on GitHub Pages, and the backend (Python, TTS, APIs) is deployed on Fly.io using Docker.</p>
    </section>
    <section class="mb-8">
      <h2 class="text-2xl font-semibold mb-2">Folder Structure</h2>
      <pre class="bg-gray-800 p-4 rounded">AgentLeeApp/
├── index.html
├── assets/
│   ├── agentlee_avatar.png
│   └── RWD_logo.png
├── xtts/
│   ├── server.py
│   ├── webhook_server.py
│   └── requirements.txt
├── Dockerfile
├── fly.toml
</pre>
    </section>
    <section class="mb-8">
      <h2 class="text-2xl font-semibold mb-2">Deployment</h2>
      <h3 class="text-xl font-bold mt-4 mb-2">Backend (Fly.io)</h3>
      <ol class="list-decimal ml-6 mb-4">
        <li>fly auth login</li>
        <li>fly launch --name agentlee-backend --no-deploy</li>
        <li>Edit fly.toml as needed</li>
        <li>fly deploy</li>
      </ol>
      <h3 class="text-xl font-bold mt-4 mb-2">Frontend (GitHub Pages)</h3>
      <ol class="list-decimal ml-6 mb-4">
        <li>Push index.html and assets/ to a GitHub repo</li>
        <li>Enable GitHub Pages in repo settings</li>
      </ol>
      <h3 class="text-xl font-bold mt-4 mb-2">Connect Frontend to Backend</h3>
      <p>Update all backend requests in index.html to use your Fly.io backend URL (e.g. <code>https://agentlee-backend.fly.dev</code>).</p>
    </section>
    <section class="mb-8">
      <h2 class="text-2xl font-semibold mb-2">API Keys</h2>
      <p>All sensitive keys are stored in <code>.env</code> and loaded automatically by Agent Lee.</p>
    </section>
    <section class="mb-8">
      <h2 class="text-2xl font-semibold mb-2">Features</h2>
      <ul class="list-disc ml-6">
        <li>Voice and vision AI</li>
        <li>Chat, TTS, file upload, social media links</li>
        <li>Deep research and workflow automation</li>
      </ul>
    </section>
    <section>
      <h2 class="text-2xl font-semibold mb-2">Contact</h2>
      <p>Built by Leonard Lee. For support, email: <a href="mailto:<EMAIL>" class="text-blue-400 underline"><EMAIL></a></p>
    </section>
  </div>
</body>
</html>
