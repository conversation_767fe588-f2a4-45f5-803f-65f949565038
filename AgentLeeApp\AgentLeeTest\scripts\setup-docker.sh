#!/bin/bash
# Setup script for Agent <PERSON> Docker environment

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp .env.sample .env
    echo "Please edit .env file with your API keys and settings"
fi

# Create data directory for persistent storage
mkdir -p data

# Build and start the containers
echo "Building and starting Agent Lee containers..."
docker-compose build
docker-compose up -d

echo "Agent <PERSON> is now running!"
echo "- Backend API: http://localhost:5000"
echo "- Frontend: http://localhost:80"
echo "- XTTS Service: http://localhost:8080"
echo "- MCP Hub: http://localhost:3000"

echo ""
echo "To view logs: docker-compose logs -f"
echo "To stop: docker-compose down"